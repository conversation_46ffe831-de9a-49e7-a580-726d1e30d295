package com.wtl.star.admin.server.job.course

import com.wtl.star.business.prime.app.course.ClassCommonAppService
import com.wtl.star.business.prime.domain.course.service.OpenClassService
import com.wtl.star.common.extensions.getTimeDiff
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import org.redisson.api.RBlockingQueue
import org.redisson.api.RDelayedQueue
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit
import kotlin.math.max

@Service
class OpenClassJobService(
    private val redissonClient: RedissonClient
) {

    private lateinit var openClassSettleDelayQueue: RDelayedQueue<Int>
    private lateinit var openClassSettleBlockingQueue: RBlockingQueue<Int>
    private lateinit var openClassSettleThread: Thread

    @Autowired
    private lateinit var openClassService: OpenClassService

    @Autowired
    private lateinit var classCommonAppService: ClassCommonAppService

    @PostConstruct
    fun init() {
        settleQueueListener()
    }

    @PreDestroy
    fun destroy() {
        openClassSettleThread.interrupt()
    }

    /**
     * 每天0点跑出在当天结算的开课信息，并发布延迟任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    fun delay1Day() {
        val startTime = LocalDateTime.now()
        val endTime = startTime.toLocalDate().atTime(LocalTime.MAX)
        val waitSettleList = openClassService.waitSettleByTime(startTime, endTime)
        if (waitSettleList.isEmpty()) {
            return
        }

        //发布延迟队列，实时完成结算
        waitSettleList.forEach { openClass ->
            val diffTime = max(1, getTimeDiff(startTime, openClass.settleTime!!, ChronoUnit.SECONDS))
            openClassSettleDelayQueue.offer(openClass.id, diffTime, TimeUnit.SECONDS)
        }
    }

    private fun settleQueueListener() {
        openClassSettleBlockingQueue = redissonClient.getBlockingQueue("open-class:settle:queue")
        openClassSettleDelayQueue = redissonClient.getDelayedQueue(openClassSettleBlockingQueue)
        openClassSettleThread = Thread {
            while (!openClassSettleThread.isInterrupted) {
                kotlin.runCatching take@{
                    // 使用 poll 方法设置超时时间，避免无限期阻塞
                    val id = openClassSettleBlockingQueue.poll(30, TimeUnit.SECONDS) ?: return@take
                    classCommonAppService.settleClass(id)
                }.onFailure {
                    if (it is InterruptedException) {
                        //抛出异常后中断状态被恢复，重新中断
                        openClassSettleThread.interrupt()
                    }
                }
            }
        }
        openClassSettleThread.start()
    }
}