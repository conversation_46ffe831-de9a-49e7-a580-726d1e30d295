package com.wtl.star.web.extensions

import com.wtl.star.web.request.RequestInfo
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.net.URLDecoder

fun currentRequest(): HttpServletRequest? {
    val servletRequestAttributes = RequestContextHolder.getRequestAttributes() as? ServletRequestAttributes
    return servletRequestAttributes?.request
}

fun currentResponse(): HttpServletResponse? {
    val servletRequestAttributes = RequestContextHolder.getRequestAttributes() as? ServletRequestAttributes
    return servletRequestAttributes?.response
}

fun HttpServletRequest.body(): String {
    val inputStream: InputStream = inputStream
    val sb = StringBuffer()
    var str: String?
    val reader = BufferedReader(InputStreamReader(inputStream, Charsets.UTF_8))
    while (reader.readLine().also { str = it } != null) {
        sb.append(str)
    }
    reader.close()
    inputStream.close()
    return sb.toString()
}

fun HttpServletRequest.param(): Map<String, String> {
    val requestParam = parameterMap
    val param: MutableMap<String, String> = mutableMapOf()
    requestParam.forEach {
        val builder = StringBuilder()
        for (str in it.value) {
            builder.append(str).append(",")
        }
        //截取符号
        var valueStr = builder.toString()
        valueStr = valueStr.substring(0, valueStr.length - 1)
        param[it.key] = valueStr
    }
    return param
}

/**
 * 获取当前客户端的设备相关信息
 */
fun currentAppRequestInfo(): RequestInfo {
    currentRequest()!!.apply {
        val info = RequestInfo()
        info.city = getHeader("city")?.run {
            URLDecoder.decode(this, "UTF-8")
        } ?: "上海"
        return info
    }
}

/**
 * 获取客户端真实 IP。
 *
 * 获取顺序：
 *
 * - 获取 http header `x-real-ip`
 * - 获取 http header `x-forwarded-for` 第一个元素
 * - 获取远程连接地址 remote address
 */
fun currentClientIp(): String {
    val request = currentRequest() ?: return ""
    var ip: String? = request.getHeader("x-forwarded-for")
    if (ip.isNullOrEmpty() || "unknown".equals(ip, ignoreCase = true)) {
        ip = request.getHeader("x-real-ip")
    }
    if (ip.isNullOrEmpty() || "unknown".equals(ip, ignoreCase = true)) {
        ip = request.getHeader("Proxy-Client-IP")
    }
    if (ip.isNullOrEmpty() || "unknown".equals(ip, ignoreCase = true)) {
        ip = request.getHeader("WL-Proxy-Client-IP")
    }
    if (ip.isNullOrEmpty() || "unknown".equals(ip, ignoreCase = true)) {
        ip = request.getHeader("HTTP_CLIENT_IP")
    }
    if (ip.isNullOrEmpty() || "unknown".equals(ip, ignoreCase = true)) {
        ip = request.getHeader("HTTP_X_FORWARDED_FOR")
    }
    if (ip.isNullOrEmpty() || "unknown".equals(ip, ignoreCase = true)) {
        ip = request.remoteAddr
    }
    return ip?.run { split(",").toSet().firstOrNull() } ?: ""
}

