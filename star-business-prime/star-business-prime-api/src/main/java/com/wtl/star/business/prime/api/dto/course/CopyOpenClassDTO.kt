package com.wtl.star.business.prime.api.dto.course

import java.time.LocalDate

/**
 * 复制开课DTO
 */
class CopyOpenClassDTO {
    // 源开始日期
    var sourceStartDate: LocalDate? = null
    
    // 源结束日期
    var sourceEndDate: LocalDate? = null
    
    // 目标开始日期
    var targetStartDate: LocalDate? = null
    
    // 目标结束日期
    var targetEndDate: LocalDate? = null
    
    // 选择的老师ID（可选）
    var teacherId: Int? = null
    
    // 选择的课程ID（可选）
    var courseId: Int? = null
    
    // 是否跳过教室时间冲突
    var skipClassroomConflict: Boolean? = null
}
