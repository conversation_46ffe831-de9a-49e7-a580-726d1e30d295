package com.wtl.star.business.prime.api.service.course

import com.mybatisflex.core.paginate.Page
import com.wtl.star.business.prime.api.bo.course.ClassRecordApplyCancelBO
import com.wtl.star.business.prime.api.bo.course.ClassRecordBO
import com.wtl.star.business.prime.api.bo.course.ClassSubscribeRecordBO
import com.wtl.star.business.prime.api.bo.course.OpenClassBO
import com.wtl.star.business.prime.api.dto.course.ClassRecordListDTO
import com.wtl.star.business.prime.api.dto.course.ClassSubscribeRecordListDTO
import com.wtl.star.business.prime.api.dto.course.OpenClassListDTO

interface OpenClassApi {

    fun list(dto: OpenClassListDTO): Page<OpenClassBO>

    fun listRecord(dto: ClassRecordListDTO): Page<ClassRecordBO>

    fun listOpenClassByIdList(idList: List<Int>): List<OpenClassBO>

    fun listSubscribeRecord(dto: ClassSubscribeRecordListDTO): Page<ClassSubscribeRecordBO>

    fun cancelClass(id: Int)

    fun teacherOpenClassPendingCount(teacherIdList: List<Int>): Map<Int, Int>

    fun teacherClassRecordCount(teacherIdList: List<Int>): Map<Int, Int>

    fun classInfo(id: Int): OpenClassBO?

    fun applyRefundListByOrderIdList(orderIdList: List<String>): List<ClassRecordApplyCancelBO>

    fun passApplyRefund(id: Int)

    fun rejectApplyRefund(id: Int)
}