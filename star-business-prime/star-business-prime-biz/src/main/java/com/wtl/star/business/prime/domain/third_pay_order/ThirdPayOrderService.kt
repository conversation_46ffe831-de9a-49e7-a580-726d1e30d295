package com.wtl.star.business.prime.domain.third_pay_order

import com.mybatisflex.core.util.UpdateEntity
import com.mybatisflex.spring.service.impl.ServiceImpl
import com.wtl.star.business.prime.domain.third_pay_order.entity.ThirdPayOrder
import com.wtl.star.business.prime.domain.third_pay_order.enums.ThirdPayOrderState
import com.wtl.star.business.prime.api.emums.third_pay_order.ThirdPayBusinessType
import com.wtl.star.business.prime.api.event.ThirdPayOrderSuccessEvent
import com.wtl.star.business.prime.domain.third_pay_order.event.ThirdPayOrderTimeOutEvent
import com.wtl.star.business.prime.domain.third_pay_order.mapper.ThirdPayOrderMapper
import com.wtl.star.common.config.Application
import com.wtl.star.common.extensions.toTimestamp
import com.wtl.star.common.util.JacksonUtil
import com.wtl.star.mybatis.extenions.condition
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import org.redisson.api.RBlockingQueue
import org.redisson.api.RDelayedQueue
import org.redisson.api.RedissonClient
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

@Service
class ThirdPayOrderService(
    private val redissonClient: RedissonClient,
    private val applicationContext: ApplicationContext,
    private val thirdPayOrderMapper: ThirdPayOrderMapper
) : ServiceImpl<ThirdPayOrderMapper, ThirdPayOrder>() {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${application:APP}")
    private var application: Application = Application.APP

    private lateinit var blockingQueue: RBlockingQueue<String>
    private lateinit var delayedQueue: RDelayedQueue<String>
    private lateinit var delayedThread: Thread

    @PostConstruct
    fun init() {
        if (application == Application.APP) {
            timeOutDelayQueueListener()
        }
    }

    @PreDestroy
    fun destroy() {
        if (application == Application.APP) {
            delayedThread.interrupt()
        }
    }

    /**
     * 订单超时延迟队列监听
     */
    private fun timeOutDelayQueueListener() {
        blockingQueue = redissonClient.getBlockingQueue("third-pay-order:time-out:queue")
        delayedQueue = redissonClient.getDelayedQueue(blockingQueue)
        delayedThread = Thread {
            while (!delayedThread.isInterrupted) {
                kotlin.runCatching take@{
                    // 使用 poll 方法设置超时时间，避免无限期阻塞
                    val orderId = blockingQueue.poll(30, TimeUnit.SECONDS) ?: return@take
                    val updateInfo = UpdateEntity.of(ThirdPayOrder::class.java)
                    updateInfo.state = ThirdPayOrderState.TIME_OUT
                    val success = thirdPayOrderMapper.updateByCondition(updateInfo, condition {
                        ThirdPayOrder::id eqA orderId
                        ThirdPayOrder::state eqA ThirdPayOrderState.PAY_ING
                    }) > 0
                    if (!success) return@take

                    val afterInfo = thirdPayOrderMapper.selectOneById(orderId)
                    applicationContext.publishEvent(
                        ThirdPayOrderTimeOutEvent(
                            afterInfo.id,
                            afterInfo.businessType,
                            afterInfo.businessParam,
                            afterInfo.money,
                            afterInfo.userId
                        )
                    )
                }.onFailure {
                    if (it is InterruptedException) {
                        //抛出异常后中断状态被恢复，重新中断
                        delayedThread.interrupt()
                    } else {
                        logger.error("timeOutDelayQueueListener处理失败", it)
                    }
                }
            }
        }
        delayedThread.start()
    }

    fun pushOrder(orderId: String, payPrice: BigDecimal, userId: Int, businessType: ThirdPayBusinessType, businessParam: Any?) {
        val thirdOrder = ThirdPayOrder(
            orderId,
            payPrice,
            userId,
            businessType = businessType,
            businessParam = businessParam?.run { JacksonUtil.toJson(this) }
        )
        thirdPayOrderMapper.insert(thirdOrder)

        delayedQueue.offer(orderId, 120, TimeUnit.SECONDS)
    }

    fun paySuccess(id: String, transactionId: String, money: BigDecimal): Boolean {
        val updateInfo = UpdateEntity.of(ThirdPayOrder::class.java)
        updateInfo.state = ThirdPayOrderState.FINISH
        updateInfo.transactionId = transactionId
        updateInfo.finishTime = LocalDateTime.now()
        val success = thirdPayOrderMapper.updateByCondition(updateInfo, condition {
            ThirdPayOrder::id eqA id
            ThirdPayOrder::state eqA ThirdPayOrderState.PAY_ING
        }) > 0
        if (!success) return success

        //成功支付，发布支付成功的事件
        val afterInfo = thirdPayOrderMapper.selectOneById(id)
        applicationContext.publishEvent(
            ThirdPayOrderSuccessEvent(
                afterInfo.id,
                transactionId,
                afterInfo.businessType,
                afterInfo.businessParam,
                money,
                afterInfo.userId
            )
        )
        return success
    }

    fun refund(id: String, refundNo: String, refundMoney: BigDecimal) {
        val before = thirdPayOrderMapper.selectOneById(id) ?: return

        val updateInfo = UpdateEntity.of(ThirdPayOrder::class.java, id)
        updateInfo.refundNo = before.refundNo?.run { "$this,$refundNo:$refundMoney" } ?: refundNo
        updateInfo.refundMoney = before.refundMoney.add(refundMoney)
        updateInfo.refundTime = before.refundTime?.run { "$this,${LocalDateTime.now().toTimestamp()}" }
        thirdPayOrderMapper.update(updateInfo)
    }
}