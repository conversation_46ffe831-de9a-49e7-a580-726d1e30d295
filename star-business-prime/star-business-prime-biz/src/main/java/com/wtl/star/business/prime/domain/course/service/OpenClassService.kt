package com.wtl.star.business.prime.domain.course.service

import com.alicp.jetcache.anno.Cached
import com.mybatisflex.core.paginate.Page
import com.mybatisflex.core.query.QueryWrapper
import com.mybatisflex.core.util.UpdateEntity
import com.mybatisflex.kotlin.extensions.condition.and
import com.mybatisflex.kotlin.extensions.db.insert
import com.mybatisflex.kotlin.extensions.kproperty.*
import com.mybatisflex.kotlin.extensions.wrapper.whereWith
import com.mybatisflex.kotlin.vec.Order
import com.mybatisflex.spring.service.impl.ServiceImpl
import com.wtl.star.business.prime.api.emums.course.OpenClassStatus
import com.wtl.star.business.prime.controller.app.course.dto.AppOpenClassDTO
import com.wtl.star.business.prime.controller.app.course.dto.AppOpenClassUpdateDTO
import com.wtl.star.business.prime.domain.course.entity.OpenClass
import com.wtl.star.business.prime.domain.course.event.CancelClassActionEvent
import com.wtl.star.business.prime.domain.course.event.OpenClassFinishEvent
import com.wtl.star.business.prime.domain.course.event.OpenClassIngEvent
import com.wtl.star.business.prime.domain.course.mapper.OpenClassMapper
import com.wtl.star.business.prime.domain.course.query.AppFilterOpenClassQuery
import com.wtl.star.business.prime.domain.course.query.OpenClassQuery
import com.wtl.star.common.config.Application
import com.wtl.star.common.config.BizRedisKey
import com.wtl.star.common.exception.BizException
import com.wtl.star.common.exception.BizExceptionConst
import com.wtl.star.common.extensions.getTimeDiff
import com.wtl.star.mybatis.extenions.condition
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import org.redisson.api.RBlockingQueue
import org.redisson.api.RDelayedQueue
import org.redisson.api.RedissonClient
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit

@Service
class OpenClassService(
    private val openClassMapper: OpenClassMapper,
    private val redissonClient: RedissonClient
) : ServiceImpl<OpenClassMapper, OpenClass>() {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Value("\${application:APP}")
    private var application: Application = Application.APP

    private lateinit var pendingToIngBlockingQueue: RBlockingQueue<Int>
    private lateinit var pendingToIngDelayedQueue: RDelayedQueue<Int>
    private lateinit var pendingToIngThread: Thread

    private lateinit var ingToFinishBlockingQueue: RBlockingQueue<Int>
    private lateinit var ingToFinishDelayedQueue: RDelayedQueue<Int>
    private lateinit var ingToFinishThread: Thread

    private lateinit var minRecordCountCheckBlockingQueue: RBlockingQueue<Int>
    private lateinit var minRecordCountCheckDelayedQueue: RDelayedQueue<Int>
    private lateinit var minRecordCheckThread: Thread

    @Autowired
    private lateinit var applicationContext: ApplicationContext

    @Autowired
    private lateinit var classRecordService: ClassRecordService

    @Autowired
    private lateinit var classSubscribeRecordService: ClassSubscribeRecordService

    @Autowired
    private lateinit var courseService: CourseService

    @PostConstruct
    fun init() {
        pendingToIngQueueListener()
        ingToFinishQueueListener()
        minRecordCheckQueueListener()
    }

    @PreDestroy
    fun destroy() {
        if (application == Application.APP) {
            pendingToIngThread.interrupt()
            ingToFinishThread.interrupt()
            minRecordCheckThread.interrupt()
        }
    }

    /**
     * 开课状态由PENDING->ING
     */
    private fun pendingToIngQueueListener() {
        pendingToIngBlockingQueue = redissonClient.getBlockingQueue("open-class:pending-to-ing:queue")
        pendingToIngDelayedQueue = redissonClient.getDelayedQueue(pendingToIngBlockingQueue)
        if (application != Application.APP) return

        pendingToIngThread = Thread {
            while (!pendingToIngThread.isInterrupted) {
                kotlin.runCatching take@{
                    val id = pendingToIngBlockingQueue.take() ?: return@take
                    logger.info("开课状态由PENDING->ING延迟队列收到通知，课程ID：$id")
                    pendingToIng(id)
                }.onFailure {
                    if (it is InterruptedException) {
                        //抛出异常后中断状态被恢复，重新中断
                        pendingToIngThread.interrupt()
                    } else {
                        logger.error("pendingToIngQueueListener处理失败", it)
                    }
                }
            }
        }
        pendingToIngThread.start()
    }

    fun pendingToIng(id: Int) {
        val updateInfo = UpdateEntity.of(OpenClass::class.java)
        updateInfo.status = OpenClassStatus.ING

        val success = update(updateInfo, condition {
            OpenClass::id eqA id
            OpenClass::status eqA OpenClassStatus.PENDING
        })

        if (success) {
            val openClass = getById(id)!!
            val finishDiffTime = getTimeDiff(LocalDateTime.now(), openClass.finishClassTime, ChronoUnit.SECONDS)
            ingToFinishDelayedQueue.offer(id, finishDiffTime, TimeUnit.SECONDS)

            //发布开课课程开始事件
            applicationContext.publishEvent(OpenClassIngEvent(id))
        }
    }

    /**
     * 开课状态由ING->FINISH
     */
    private fun ingToFinishQueueListener() {
        ingToFinishBlockingQueue = redissonClient.getBlockingQueue("open-class:ing-to-finish:queue")
        ingToFinishDelayedQueue = redissonClient.getDelayedQueue(ingToFinishBlockingQueue)
        if (application != Application.APP) return

        ingToFinishThread = Thread {
            while (!ingToFinishThread.isInterrupted) {
                kotlin.runCatching take@{
                    val id = ingToFinishBlockingQueue.take() ?: return@take
                    logger.info("开课状态由ING->FINISH延迟队列收到通知，课程ID：$id")
                    val updateInfo = UpdateEntity.of(OpenClass::class.java)
                    updateInfo.status = OpenClassStatus.FINISH
                    //设置结算时间
                    updateInfo.settleTime = LocalDateTime.now().plusDays(7)
                    val success = update(updateInfo, condition {
                        OpenClass::id eqA id
                        OpenClass::status eqA OpenClassStatus.ING
                    })
                    if (success) {
                        val openClass = getById(id)!!
                        courseService.updateLastAttendClassTime(openClass.courseId, openClass.attendClassTime)
                        classRecordService.finishByClassId(id)
                        applicationContext.publishEvent(OpenClassFinishEvent(id, openClass.siteId, openClass.danceRoomId))
                    }
                }.onFailure {
                    if (it is InterruptedException) {
                        //抛出异常后中断状态被恢复，重新中断
                        ingToFinishThread.interrupt()
                    } else {
                        logger.error("ingToFinishQueueListener处理失败", it)
                    }
                }
            }
        }
        ingToFinishThread.start()
    }

    /**
     * 最小预约人数检查，未达到最小预约人数取消开课
     */
    private fun minRecordCheckQueueListener() {
        minRecordCountCheckBlockingQueue = redissonClient.getBlockingQueue("open-class:min-record-check:queue")
        minRecordCountCheckDelayedQueue = redissonClient.getDelayedQueue(minRecordCountCheckBlockingQueue)
        if (application != Application.APP) return

        minRecordCheckThread = Thread {
            while (!minRecordCheckThread.isInterrupted) {
                kotlin.runCatching take@{
                    // 使用 poll 方法设置超时时间，避免无限期阻塞
                    val id = minRecordCountCheckBlockingQueue.poll(30, TimeUnit.SECONDS) ?: return@take
                    logger.info("最小开课人数检查延迟队列收到通知，课程ID：$id")

                    val openClass = getById(id)
                    if (openClass == null) {
                        logger.warn("未找到课程信息，课程ID：$id")
                        return@take
                    }

                    logger.info("课程ID：$id, 当前约课人数：${openClass.recordCount}, 最小开课人数：${openClass.minRecordCount}")
                    if (openClass.recordCount < openClass.minRecordCount) {
                        logger.info("课程ID：$id 约课人数未达到最小开课人数，准备解散课程")
                        applicationContext.publishEvent(CancelClassActionEvent(id, "约课人数未到最小开课人数自动解散课程", 4))
                        logger.info("课程ID：$id 已发送解散事件，处理完成")
                    } else {
                        logger.info("课程ID：$id 约课人数已达到最小开课人数，不需要解散，处理完成")
                    }
                }.onFailure {
                    if (it is InterruptedException) {
                        //抛出异常后中断状态被恢复，重新中断
                        minRecordCheckThread.interrupt()
                    } else {
                        logger.error("minRecordCheckQueueListener处理失败", it)
                    }
                }
            }
        }
        minRecordCheckThread.start()
    }

    fun list(query: OpenClassQuery): Page<OpenClass> {
        val param = createQueryWrapper(query)

        param.orderBy(OpenClass::attendClassTime.toOrd(Order.DESC))
        return openClassMapper.paginate(query.page, query.size, param)
    }

    private fun createQueryWrapper(query: OpenClassQuery): QueryWrapper {
        val param = QueryWrapper.create()
        query.id?.run {
            param.whereWith {
                OpenClass::id eq this
            }
        }
        query.courseId?.run {
            param.whereWith {
                OpenClass::courseId eq this
            }
        }
        query.teacherId?.run {
            param.whereWith {
                OpenClass::teacherId eq this
            }
        }
        query.status?.run {
            param.whereWith {
                OpenClass::status eq this
            }
        }
        query.siteId?.run {
            param.whereWith {
                OpenClass::siteId eq this
            }
        }
        query.ids?.run {
            param.whereWith {
                OpenClass::id `in` this
            }
        }
        query.gteRecordCount?.run {
            param.whereWith {
                OpenClass::recordCount ge this
            }
        }
        if (query.attendClassStartTime != null && query.attendClassEndTime != null) {
            param.whereWith {
                OpenClass::attendClassTime between query.attendClassStartTime!!..query.attendClassEndTime!!
            }
        }
        if (query.createStartTime != null && query.createEndTime != null) {
            param.whereWith {
                OpenClass::createTime between query.createStartTime!!..query.createEndTime!!
            }
        }
        return param
    }

    fun cancel(id: Int, checkStatus: OpenClassStatus?): Boolean {
        val updateInfo = UpdateEntity.of(OpenClass::class.java)
        updateInfo.status = OpenClassStatus.CANCEL
        updateInfo.recordCount = 0
        updateInfo.subscribeCount = 0

        val param = condition {
            OpenClass::id eqA id
            OpenClass::status neA OpenClassStatus.CANCEL
        }
        checkStatus?.run {
            param.and {
                OpenClass::status eq this
            }
        }
        val success = openClassMapper.updateByCondition(updateInfo, param) > 0

        if (success) {
            pendingToIngDelayedQueue.remove(id)
            ingToFinishDelayedQueue.remove(id)
            minRecordCountCheckDelayedQueue.remove(id)
        }
        return success
    }

    fun removeDelayedQueue(id: Int) {
        pendingToIngDelayedQueue.remove(id)
        ingToFinishDelayedQueue.remove(id)
        minRecordCountCheckDelayedQueue.remove(id)
    }

    fun selectByIdList(idList: List<Int>): List<OpenClass> {
        if (idList.isEmpty()) return emptyList()

        return openClassMapper.selectListByIds(idList)
    }

    @Cached(name = BizRedisKey.APP_GUESS_YOU_LIKE, key = "#userId + '-' + #city", expire = 30, timeUnit = TimeUnit.MINUTES)
    fun appGuessYouLike(userId: Int, city: String): List<Int> {
        val allList = openClassMapper.appGuessYouLike(city)
        return appOpenClassSort(userId, allList).map { it.id }
    }

    @Cached(name = BizRedisKey.APP_HOT_REGISTRATION, key = "#userId + '-' + #city", expire = 30, timeUnit = TimeUnit.MINUTES)
    fun appHotRegistration(userId: Int, city: String): List<Int> {
        val allList = openClassMapper.appHotRegistration(city)
        val allIdList = allList.map { it.id }
        val recordList = classRecordService.selectSuccessListByStudentIdAndClassIdList(userId, allIdList)
        val subscribeRecordList = classSubscribeRecordService.selectIngByStudentIdAndClassIdList(userId, allIdList)
        return allList.sortedWith(
            compareByDescending<OpenClass> { openClass ->
                //优先展示当前用户无订单关联此开课信息的课程
                if (recordList.none { it.classId == openClass.id } && subscribeRecordList.none { it.classId == openClass.id }) {
                    return@compareByDescending 100
                }
                //再依次展示用户已预约的、排队中的课程
                if (recordList.any { it.classId == openClass.id } || subscribeRecordList.any { it.classId == openClass.id }) {
                    return@compareByDescending 50
                }
                0
            }.thenByDescending {
                //优先展示未满员的，再展示已满员的
                if (it.recordCount < it.number) {
                    return@thenByDescending 50
                }
                0
            }.thenByDescending {
                it.recordCount / it.number
            }.thenBy {
                //按照上课时间从近到远排序
                it.attendClassTime
            }.thenByDescending {
                //按照开课时间从晚到早排序
                it.createTime
            }
        ).map { it.id }
    }

    @Cached(name = BizRedisKey.APP_MY_TEACHER, key = "#userId + '-' + #city", expire = 30, timeUnit = TimeUnit.MINUTES)
    fun appMyTeacher(userId: Int, city: String, followTeacherIdList: List<Int>): List<Int> {
        val classIdList = classRecordService.selectSuccessListByStudentId(userId).map { it.classId }
        if (classIdList.isEmpty()) return emptyList()

        val classList = openClassMapper.selectListByIds(classIdList)
        val teacherIdList = listOf(followTeacherIdList, classList.map { it.teacherId }).flatten()
        if (teacherIdList.isEmpty()) return emptyList()
        val allList = openClassMapper.appMyTeacher(city, teacherIdList)
        return appOpenClassSort(userId, allList).map { it.id }
    }

    fun appMyHistory(userId: Int, page: Int, size: Int): Page<OpenClass> {
        val param = mapOf("userId" to userId)
        return openClassMapper.xmlPaginate("appMyHistory", Page.of(page, size), param)
    }

    private fun appOpenClassSort(userId: Int, allList: List<OpenClass>): List<OpenClass> {
        val allIdList = allList.map { it.id }
        val recordList = classRecordService.selectSuccessListByStudentIdAndClassIdList(userId, allIdList)
        val subscribeRecordList = classSubscribeRecordService.selectIngByStudentIdAndClassIdList(userId, allIdList)
        return allList.sortedWith(
            compareByDescending<OpenClass> { openClass ->
                //优先展示当前用户无订单关联此开课信息的课程
                if (recordList.none { it.classId == openClass.id } && subscribeRecordList.none { it.classId == openClass.id }) {
                    return@compareByDescending 100
                }
                //再依次展示用户已预约的、排队中的课程
                if (recordList.any { it.classId == openClass.id } || subscribeRecordList.any { it.classId == openClass.id }) {
                    return@compareByDescending 50
                }
                0
            }.thenByDescending {
                //优先展示未满员的，再展示已满员的
                if (it.recordCount < it.number) {
                    return@thenByDescending 50
                }
                0
            }.thenBy {
                //按照上课时间从近到远排序
                it.attendClassTime
            }.thenByDescending {
                //按照开课时间从晚到早排序
                it.createTime
            }
        )
    }

    fun appSearch(param: String, page: Int = 1, size: Int = 10): Page<OpenClass> {
        return openClassMapper.xmlPaginate("appSearch", Page.of(page, size), mapOf("param" to param))
    }

    @Cached(name = BizRedisKey.APP_FILTER, key = "#userId + '-' + #requestId", expire = 30, timeUnit = TimeUnit.MINUTES)
    fun appFilter(userId: Int, requestId: String, query: AppFilterOpenClassQuery): List<Int> {
        val classList = openClassMapper.appFilter(query)
        //排序 0：智能排序 1：低价优先 2：高价优先 3：评分优先 4：人气优先
        return when (query.sort) {
            0 -> appOpenClassSort(userId, classList)
            1 -> {
                val priceSortClassList = classList.sortedBy { it.price }
                appOpenClassSort(userId, priceSortClassList)
            }

            2 -> {
                val priceSortClassList = classList.sortedByDescending { it.price }
                appOpenClassSort(userId, priceSortClassList)
            }

            3 -> {
                val evaluateSortClassList = classList.sortedByDescending { it.evaluateScore }
                appOpenClassSort(userId, evaluateSortClassList)
            }

            4 -> {
                val evaluateSortClassList = classList.sortedByDescending { it.recordCount / it.number }
                appOpenClassSort(userId, evaluateSortClassList)
            }

            else -> throw BizException(BizExceptionConst.REQUEST_PARAM_ERROR)
        }.map { it.id }
    }

    fun teacherOpenClassPendingCount(teacherIdList: List<Int>): Map<Int, Int> {
        if (teacherIdList.isEmpty()) return emptyMap()
        return openClassMapper.teacherOpenClassPendingCount(teacherIdList).associateBy({ it.teacherId }, { it.count })
    }

    fun addEvaluate(openClass: OpenClass, score: Int) {
        val thisScore = openClass.evaluateTotalScore + score
        val thisCount = openClass.evaluateTotalCount + 1
        val thisScoreAvg = BigDecimal(thisScore).divide(BigDecimal(thisCount)).setScale(2, RoundingMode.HALF_UP)
        val updateInfo = UpdateEntity.of(OpenClass::class.java, openClass.id)
        updateInfo.evaluateTotalScore = thisScore
        updateInfo.evaluateTotalCount = thisCount
        updateInfo.evaluateScore = thisScoreAvg
        openClassMapper.update(updateInfo)
    }

    fun addRecordCount(id: Int, courseId: Int, money: BigDecimal) {
        openClassMapper.addRecordCount(id, money)

        courseService.addRecordCount(courseId)
    }

    fun minRecordCount(id: Int, courseId: Int, money: BigDecimal) {
        openClassMapper.minRecordCount(id, money)

        courseService.minusRecordCount(courseId)
    }

    fun addSubscribeCount(id: Int) {
        openClassMapper.addSubscribeCount(id)
    }

    fun minSubscribeCount(id: Int) {
        openClassMapper.minSubscribeCount(id)
    }

    fun addGroupSettleMoney(id: Int, money: BigDecimal) {
        openClassMapper.addGroupSettleMoney(id, money)
    }

    fun selectListAllByStatus(status: OpenClassStatus): List<OpenClass> {
        return openClassMapper.selectListByCondition(OpenClass::status eq status)
    }

    fun appOpenClassRecord(userId: Int, date: LocalDate, name: String?): List<OpenClass> {
        val startDateTime = date.atTime(LocalTime.MIN)
        val endDateTime = date.atTime(LocalTime.MAX)
        return openClassMapper.appOpenClassRecord(userId, startDateTime, endDateTime, name)
    }

    fun appOpenClass(dto: AppOpenClassDTO, intoTeacherId: Int): OpenClass {
        val nowDateTime = LocalDateTime.now()
        val openClass = OpenClass().apply {
            courseId = dto.courseId
            teacherId = intoTeacherId
            price = dto.price
            memberPrice = dto.memberPrice
            advanceBookingHours = dto.advanceBookingHours
            siteId = dto.siteId
            danceRoomId = dto.danceRoomId
            customCityCode = dto.customCityCode
            customSiteProvinceAndCity = dto.customSiteProvinceAndCity
            customSiteCounty = dto.customSiteCounty
            customSiteName = dto.customSiteName
            customSiteAddress = dto.customSiteAddress
            customSiteRemark = dto.customSiteRemark
            customDanceRoomName = dto.customDanceRoomName
            attendClassTime = dto.date.atTime(dto.startTime)
            finishClassTime = dto.date.atTime(dto.endTime)
            number = dto.maxNumber
            columnNumber = dto.columnNumber
            rowNumber = dto.rowNumber
            evaluateScore = BigDecimal.valueOf(4.0)
            allowGroup = dto.openGroup
            minRecordCount = dto.minRecordCount
            atLatestCancelTime = dto.atLatestCancelTime
            createTime = nowDateTime
            bookingRestrictions = dto.bookingRestrictions
        }
        if (openClass.attendClassTime <= nowDateTime) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "上课时间不能早于当前时间，请检查后重新发起")
        }
        if (openClass.finishClassTime <= openClass.attendClassTime) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "下课时间不能早于上课时间，请检查后重新发起")
        }
        if (openClass.atLatestCancelTime >= openClass.attendClassTime) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "最晚解散时间不能晚于上课时间，请检查后重新发起")
        }
        if (openClass.atLatestCancelTime <= nowDateTime) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "最晚解散时间不能早于当前时间，请检查后重新发起")
        }

        insert(openClass)

        val nowTime = LocalDateTime.now()
        //发布状态变化为通话中延迟队列
        val ingDiffTime = getTimeDiff(nowTime, openClass.attendClassTime, ChronoUnit.SECONDS)
        pendingToIngDelayedQueue.offer(openClass.id, ingDiffTime, TimeUnit.SECONDS)
        //发布最小开课人数检查延迟队列
        val minRecordCheckDiffTime = getTimeDiff(nowTime, openClass.atLatestCancelTime, ChronoUnit.SECONDS)
        minRecordCountCheckDelayedQueue.offer(openClass.id, minRecordCheckDiffTime, TimeUnit.SECONDS)
        return openClass
    }

    fun appModifyOpenClass(openClass: OpenClass, dto: AppOpenClassUpdateDTO) {
        if (openClass.status != OpenClassStatus.PENDING) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课状态为非待开课状态，无法修改开课信息")
        }

        val updateInfo = UpdateEntity.of(OpenClass::class.java)
        dto.customCityCode?.run {
            updateInfo.customCityCode = this
        }
        dto.customSiteProvinceAndCity?.run {
            if (openClass.siteId != null) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "该开课信息已绑定场地，无法修改场地信息")
            }
            updateInfo.customSiteProvinceAndCity = this
        }
        dto.customSiteCounty?.run {
            updateInfo.customSiteCounty = this
        }
        dto.customSiteAddress?.run {
            updateInfo.customSiteAddress = this
        }
        dto.customSiteName?.run {
            updateInfo.customSiteName = this
        }
        dto.customSiteRemark?.run {
            updateInfo.customSiteRemark = this
        }
        dto.customDanceRoomName?.run {
            updateInfo.customDanceRoomName = this
        }
        dto.price?.run {
            updateInfo.price = this
        }
        dto.memberPrice?.run {
            updateInfo.memberPrice = this
        }
        dto.advanceBookingHours?.run {
            updateInfo.advanceBookingHours = this
        }
        dto.bookingRestrictions?.run {
            updateInfo.bookingRestrictions = this
        }
        val nowDateTime = LocalDateTime.now()
        var attendClassTime: LocalDateTime? = null
        var finishClassTime: LocalDateTime? = null
        if (dto.date != null && dto.startTime != null && dto.endTime != null) {
            attendClassTime = dto.date!!.atTime(dto.startTime)
            finishClassTime = dto.date!!.atTime(dto.endTime)
            if (attendClassTime <= nowDateTime) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "上课时间不能早于当前时间，请检查后重新发起")
            }
            if (finishClassTime <= attendClassTime) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "下课时间不能早于上课时间，请检查后重新发起")
            }
            updateInfo.attendClassTime = attendClassTime
            updateInfo.finishClassTime = finishClassTime
        }
        dto.minRecordCount?.run {
            if (this > openClass.number) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "最小预约人数不能大于班级人数，请检查后重新发起")
            }
            updateInfo.minRecordCount = this
        }
        dto.atLatestCancelTime?.run {
            if (this >= attendClassTime) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "最晚解散时间不能晚于上课时间，请检查后重新发起")
            }
            if (this <= nowDateTime) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "最晚解散时间不能早于当前时间，请检查后重新发起")
            }
            updateInfo.atLatestCancelTime = this
        }
        val success = openClassMapper.updateByCondition(updateInfo, condition {
            OpenClass::id eqA openClass.id
            OpenClass::status eqA OpenClassStatus.PENDING
        }) > 0
        if (!success) throw BizException(BizExceptionConst.CUSTOM_ERROR, "修改失败，请稍后重试")

        //队列信息变更
        if (attendClassTime != null && finishClassTime != null) {
            if (openClass.attendClassTime != attendClassTime || openClass.finishClassTime != finishClassTime) {
                pendingToIngDelayedQueue.remove(openClass.id)
                //发布状态变化为通话中延迟队列
                val ingDiffTime = getTimeDiff(nowDateTime, attendClassTime, ChronoUnit.SECONDS)
                pendingToIngDelayedQueue.offer(openClass.id, ingDiffTime, TimeUnit.SECONDS)
            }
        }
        dto.atLatestCancelTime?.run {
            minRecordCountCheckDelayedQueue.remove(openClass.id)
            //发布最小开课人数检查延迟队列
            val minRecordCheckDiffTime = getTimeDiff(nowDateTime, this, ChronoUnit.SECONDS)
            minRecordCountCheckDelayedQueue.offer(openClass.id, minRecordCheckDiffTime, TimeUnit.SECONDS)
        }
    }

    fun signIn(id: Int, img: String) {
        val updateInfo = UpdateEntity.of(OpenClass::class.java, id)
        updateInfo.signInImg = img
        updateInfo.signInTime = LocalDateTime.now()
        openClassMapper.update(updateInfo)
    }

    fun countPendingByDanceRoomIdList(danceRoomIdList: List<Int>): Map<Int, Int> {
        if (danceRoomIdList.isEmpty()) return emptyMap()

        return openClassMapper.countPendingByDanceRoomIdListAndTime(danceRoomIdList).associate { it.danceRoomId to it.count }
    }

    fun countPendingBySiteIdList(siteIdList: List<Int>): Map<Int, Int> {
        if (siteIdList.isEmpty()) return emptyMap()

        return openClassMapper.countPendingBySiteIdList(siteIdList).associate { it.siteId to it.count }
    }

    fun pendingClassByDanceRoomId(danceRoomId: Int): List<OpenClass> {
        return openClassMapper.selectListByCondition(OpenClass::danceRoomId eq danceRoomId)
    }

    fun pendingClassBySiteId(siteId: Int): List<OpenClass> {
        return openClassMapper.selectListByCondition(OpenClass::siteId eq siteId)
    }

    fun waitSettleByTime(startTime: LocalDateTime, endTime: LocalDateTime): List<OpenClass> {
        return openClassMapper.selectListByCondition(
            condition {
                OpenClass::settleTime gtA startTime
                OpenClass::settleTime ltA endTime
                OpenClass::hasSettle eqA false
            }
        )
    }

    fun settle(id: Int): Boolean {
        val updateInfo = UpdateEntity.of(OpenClass::class.java)
        updateInfo.hasSettle = true
        return openClassMapper.updateByCondition(updateInfo, condition {
            OpenClass::id eqA id
            OpenClass::hasSettle eqA false
            OpenClass::status eqA OpenClassStatus.FINISH
        }) > 0
    }

    fun allNeCancelClassList(): List<OpenClass> {
        return openClassMapper.selectListByCondition(OpenClass::status ne OpenClassStatus.CANCEL)
    }

    fun setRecordCountAndTotalMoneyAndSubscribeCount(id: Int, money: BigDecimal, subscribeCount: Int, recordCount: Int) {
        val updateInfo = UpdateEntity.of(OpenClass::class.java, id)
        updateInfo.totalMoney = money
        updateInfo.subscribeCount = subscribeCount
        updateInfo.recordCount = recordCount
        openClassMapper.update(updateInfo)
    }
}