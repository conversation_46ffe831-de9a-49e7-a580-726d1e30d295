package com.wtl.star.business.prime.app.course

import com.mybatisflex.core.paginate.Page
import com.mybatisflex.core.query.QueryWrapper
import com.mybatisflex.kotlin.extensions.kproperty.eq
import com.mybatisflex.kotlin.extensions.kproperty.ge
import com.mybatisflex.kotlin.extensions.kproperty.like
import com.mybatisflex.kotlin.extensions.kproperty.lt
import com.mybatisflex.kotlin.extensions.kproperty.ne
import com.mybatisflex.kotlin.extensions.wrapper.whereWith
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse
import com.wtl.star.busimess.user.bo.teacher.TeacherBO
import com.wtl.star.busimess.user.entity.UserWalletLogType
import com.wtl.star.busimess.user.service.teacher.TeacherApi
import com.wtl.star.busimess.user.service.user.UserApi
import com.wtl.star.business.admin.api.service.card_course.CardCourseApi
import com.wtl.star.business.prime.api.bo.course.ClassRecordBO
import com.wtl.star.business.prime.api.bo.course.ClassSubscribeRecordBO
import com.wtl.star.business.prime.api.bo.course.OpenClassBO
import com.wtl.star.business.prime.api.bo.course.OpenClassItemBO
import com.wtl.star.business.prime.api.bo.course.OpenClassTimetableBO
import com.wtl.star.business.prime.api.dto.course.ClassRecordListDTO
import com.wtl.star.business.prime.api.dto.course.ClassSubscribeRecordListDTO
import com.wtl.star.business.prime.api.dto.course.CopyOpenClassDTO
import com.wtl.star.business.prime.api.dto.course.OpenClassListDTO
import com.wtl.star.business.prime.api.dto.course.OpenClassTimetableQueryDTO
import com.wtl.star.business.prime.api.emums.business_order.BusinessOrderPayType
import com.wtl.star.business.prime.api.emums.business_order.BusinessOrderPaymentType
import com.wtl.star.business.prime.api.emums.business_order.BusinessOrderState
import com.wtl.star.business.prime.api.emums.business_order.BusinessOrderType
import com.wtl.star.business.prime.api.emums.course.ClassRecordApplyCancelStatus
import com.wtl.star.business.prime.api.emums.course.ClassRecordStatus
import com.wtl.star.business.prime.api.emums.course.ClassSubscribeRecordStatus
import com.wtl.star.business.prime.api.emums.course.OpenClassStatus
import com.wtl.star.business.prime.api.emums.evaluate.EvaluateType
import com.wtl.star.business.prime.app.business_order.BusinessOrderCommonAppService
import com.wtl.star.business.prime.app.site.DanceRoomCommonAppService
import com.wtl.star.business.prime.controller.app.course.dto.*
import com.wtl.star.business.prime.controller.app.course.vo.*
import com.wtl.star.business.prime.domain.business_order.entity.BusinessOrder
import com.wtl.star.business.prime.domain.business_order.entity.BusinessOrderPaymentItem
import com.wtl.star.business.prime.domain.business_order.service.BusinessOrderService
import com.wtl.star.business.prime.domain.course.entity.*
import com.wtl.star.business.prime.domain.course.event.CancelClassActionEvent
import com.wtl.star.business.prime.domain.course.event.OpenClassIngEvent
import com.wtl.star.business.prime.domain.course.query.*
import com.wtl.star.business.prime.domain.course.service.*
import com.wtl.star.business.prime.domain.evaluate.entity.EvaluateRecord
import com.wtl.star.business.prime.domain.evaluate.service.EvaluateRecordService
import com.wtl.star.business.prime.domain.group.service.ClassGroupInfoService
import com.wtl.star.business.prime.domain.group.service.ClassGroupMemberService
import com.wtl.star.business.prime.domain.site.entity.DanceRoom
import com.wtl.star.business.prime.domain.site.entity.Site
import com.wtl.star.business.prime.domain.site.service.DanceRoomService
import com.wtl.star.business.prime.domain.site.service.DanceRoomTimeInfoService
import com.wtl.star.business.prime.domain.site.service.SiteService
import com.wtl.star.business.prime.domain.system.service.PlatformInformService
import com.wtl.star.business.prime.domain.third_pay_order.ThirdPayOrderService
import com.wtl.star.business.prime.domain.third_pay_order.config.IdGenerator
import com.wtl.star.business.prime.api.emums.third_pay_order.ThirdPayBusinessType
import com.wtl.star.business.prime.api.event.ThirdPayOrderSuccessEvent
import com.wtl.star.business.prime.domain.third_pay_order.event.ThirdPayOrderTimeOutEvent
import com.wtl.star.common.api.ApiPage
import com.wtl.star.common.config.BizRedisKey
import com.wtl.star.common.exception.BizException
import com.wtl.star.common.exception.BizExceptionConst
import com.wtl.star.common.extensions.*
import com.wtl.star.common.util.JacksonUtil
import com.wtl.star.common.util.ObjectId
import com.wtl.star.mybatis.extenions.build
import com.wtl.star.pay.wei_xin.service.WeiXinService
import com.wtl.star.web.extensions.currentAppRequestInfo
import com.wtl.star.web.extensions.currentAppUser
import org.redisson.api.RedissonClient
import org.springframework.beans.BeanUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate
import java.time.DayOfWeek
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import com.wtl.star.business.prime.domain.course.entity.OpenClass
import org.slf4j.LoggerFactory
import java.time.LocalTime

@Service
class OpenClassAppService {

    private val log = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var redissonClient: RedissonClient

    @Autowired
    private lateinit var openClassService: OpenClassService

    @Autowired
    private lateinit var classRecordService: ClassRecordService

    @Autowired
    private lateinit var classSubscribeRecordService: ClassSubscribeRecordService

    @Autowired
    private lateinit var siteService: SiteService

    @Autowired
    private lateinit var teacherApi: TeacherApi

    @Autowired
    private lateinit var userApi: UserApi

    @Autowired
    private lateinit var courseService: CourseService

    @Autowired
    private lateinit var courseFileService: CourseFileService

    @Autowired
    private lateinit var danceRoomService: DanceRoomService

    @Autowired(required = false)
    private lateinit var idGenerator: IdGenerator

    @Autowired(required = false)
    private lateinit var weiXinService: WeiXinService

    @Autowired
    private lateinit var thirdPayOrderService: ThirdPayOrderService

    @Autowired
    private lateinit var businessOrderService: BusinessOrderService

    @Autowired
    private lateinit var classRecordCommonService: ClassRecordCommonService

    @Autowired
    private lateinit var openClassFileService: OpenClassFileService

    @Autowired
    private lateinit var evaluateRecordService: EvaluateRecordService

    @Autowired
    private lateinit var classRecordApplyCancelService: ClassRecordApplyCancelService

    @Autowired
    private lateinit var classCommonAppService: ClassCommonAppService

    @Autowired
    private lateinit var classGroupInfoService: ClassGroupInfoService

    @Autowired
    private lateinit var classGroupMemberService: ClassGroupMemberService

    @Autowired
    private lateinit var platformInformService: PlatformInformService

    @Autowired
    private lateinit var danceRoomTimeInfoService: DanceRoomTimeInfoService

    @Autowired
    private lateinit var danceRoomCommonAppService: DanceRoomCommonAppService

    @Autowired
    private lateinit var businessOrderCommonAppService: BusinessOrderCommonAppService

    @Autowired
    private lateinit var cardCourseApi: CardCourseApi

    fun list(dto: OpenClassListDTO): Page<OpenClassBO> {
        val query = OpenClassQuery()
        BeanUtils.copyProperties(dto, query)
        dto.studentId?.run {
            val thisClassIds = classRecordService.selectClassIdsByStudentId(this)
            if (thisClassIds.isNotEmpty()) {
                query.ids = thisClassIds
            }
        }
        val result = openClassService.list(query)
        val teacherIds = result.records.map { it.teacherId }
        val siteIds = result.records.mapNotNull { it.siteId }
        val courseIds = result.records.mapNotNull { it.courseId }
        val teachers = teacherApi.findByIds(teacherIds)
        val sites = siteService.selectByIdList(siteIds)
        val courses = courseService.selectByIdList(courseIds)
        return result.map { openClass ->
            val bo = OpenClassBO()
            BeanUtils.copyProperties(openClass, bo)
            val teacher = teachers.first { it.id == openClass.teacherId }
            bo.teacherName = teacher.nickname
            val site = sites.firstOrNull { it.id == openClass.siteId }
            bo.siteBO = site?.convertToBO()
            courses.firstOrNull { it.id == openClass.courseId }?.run {
                bo.courseBO = this.convertToBO()!!
            }
            bo
        }
    }

    fun listRecord(dto: ClassRecordListDTO): Page<ClassRecordBO> {
        val query = ClassRecordQuery()
        BeanUtils.copyProperties(dto, query)
        query.statuss = listOf(
            ClassRecordStatus.SUCCESS,
            ClassRecordStatus.FINISH
        )
        val result = classRecordService.list(query)
        val studentIds = result.records.map { it.studentId }
        val students = userApi.findByIdList(studentIds)
        return result.map { record ->
            val bo = ClassRecordBO()
            BeanUtils.copyProperties(record, bo)
            val student = students.first { it.id == record.studentId }
            bo.studentName = student.nickname
            bo
        }
    }

    fun listRecordByIdList(idList: List<Int>): List<OpenClassBO> {
        return openClassService.selectByIdList(idList).mapNotNull {
            it.convertToBO<OpenClassBO>()
        }
    }


    fun listSubscribeRecord(dto: ClassSubscribeRecordListDTO): Page<ClassSubscribeRecordBO> {
        val query = ClassSubscribeRecordQuery()
        BeanUtils.copyProperties(dto, query)
        query.status = ClassSubscribeRecordStatus.ING
        val result = classSubscribeRecordService.list(query)
        val studentIds = result.records.map { it.studentId }
        val students = userApi.findByIdList(studentIds)
        return result.map { record ->
            val bo = ClassSubscribeRecordBO()
            BeanUtils.copyProperties(record, bo)
            val student = students.first { it.id == record.studentId }
            bo.studentName = student.nickname
            bo
        }
    }

    /**
     * 取消开课
     */
    fun cancelClass(id: Int) {
        classCommonAppService.cancelClass(id, "admin取消开课", 2)
    }

    /**
     * 删除开课，如果有人约课则不允许删除
     * @param id 开课ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = [Exception::class])
    fun deleteClass(id: Int): Boolean {
        // 检查开课是否存在
        openClassService.getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课记录不存在")
        
        // 检查是否有人约课
        val records = classRecordService.selectSuccessListByClassId(id)
        if (records.isNotEmpty()) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该开课已有人约课，无法删除")
        }
        
        // 直接删除开课记录
        val success = openClassService.removeById(id)
        if (success) {
            openClassService.removeDelayedQueue(id)
        }
        return success
    }

    /**
     * app猜你喜欢
     */
    fun appGuessYouLike(page: Int, size: Int): ApiPage<AppOpenClassSimpleInfoVO> {
        val (result, total) = appClassRecordVO("GUESS_YOU_LIKE", page, size)
        return ApiPage(page, size, total, result)
    }

    /**
     * app火热报名
     */
    fun appHotRegistration(page: Int, size: Int): ApiPage<AppOpenClassSimpleInfoVO> {
        val (result, total) = appClassRecordVO("HOT_REGISTRATION", page, size)
        return ApiPage(page, size, total, result)
    }

    /**
     * app我的老师
     */
    fun appMyTeacher(page: Int, size: Int): ApiPage<AppOpenClassSimpleInfoVO> {
        val (result, total) = appClassRecordVO("MY_TEACHER", page, size)
        return ApiPage(page, size, total, result)
    }

    /**
     * app历史上过
     */
    fun appMyHistory(page: Int, size: Int): ApiPage<AppOpenClassSimpleInfoVO> {
        val userId = currentAppUser().id
        val result = openClassService.appMyHistory(currentAppUser().id, page, size)
        val openClassList = result.records
        val teacherIds = openClassList.map { it.teacherId }
        val teachers = teacherApi.findByIds(teacherIds)
        val courseIds = openClassList.map { it.courseId }
        val courses = courseService.selectByIdList(courseIds)
        val courseFileList = courseFileService.selectListByCourseIds(courseIds)
        val siteIds = openClassList.mapNotNull { it.siteId }
        val sites = siteService.selectByIdList(siteIds)
        val openClassIds = openClassList.map { it.id }
        val classRecords = classRecordService.selectSuccessListByStudentIdAndClassIdList(userId, openClassIds)
        val subscribeRecords = classSubscribeRecordService.selectIngByStudentIdAndClassIdList(userId, openClassIds)

        val vos = result.records.map { openClass ->
            val teacher = teachers.first { it.id == openClass.teacherId }
            val course = courses.first { it.id == openClass.courseId }
            val courseImgs = courseFileList.filter { it.courseId == openClass.courseId && it.type == "image" }
            val site = openClass.siteId?.run {
                sites.first { it.id == openClass.siteId }
            }
            val classRecord = classRecords.firstOrNull { it.classId == openClass.id }
            val subscribeRecord = subscribeRecords.firstOrNull { it.classId == openClass.id }
            setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgs, site, classRecord, subscribeRecord)
        }

        return result.build(vos)
    }

    fun appClassRecordVO(source: String, page: Int, size: Int): Pair<List<AppOpenClassSimpleInfoVO>, Int> {
        val userId = currentAppUser().id
        val allList = when (source) {
            "GUESS_YOU_LIKE"   -> openClassService.appGuessYouLike(userId, currentAppRequestInfo().city)
            "HOT_REGISTRATION" -> openClassService.appHotRegistration(userId, currentAppRequestInfo().city)
            "MY_TEACHER"       -> {
                val followTeacherIdList = teacherApi.followTeacherIdList(userId)
                openClassService.appMyTeacher(userId, currentAppRequestInfo().city, followTeacherIdList)
            }

            else               -> throw BizException(BizExceptionConst.REQUEST_PARAM_ERROR)
        }
        val startIndex = (page - 1) * size
        var endIndex = startIndex + size
        if (endIndex > allList.size) {
            endIndex = allList.size
        }
        val openClassIdList = allList.subList(startIndex, endIndex)
        val openClassList = openClassService.selectByIdList(openClassIdList)
        val teacherIds = openClassList.map { it.teacherId }
        val teachers = teacherApi.findByIds(teacherIds)
        val courseIds = openClassList.map { it.courseId }
        val courses = courseService.selectByIdList(courseIds)
        val courseFileList = courseFileService.selectListByCourseIds(courseIds)
        val siteIds = openClassList.mapNotNull { it.siteId }
        val sites = siteService.selectByIdList(siteIds)
        val classRecords = classRecordService.selectSuccessListByStudentIdAndClassIdList(userId, openClassIdList)
        val subscribeRecords = classSubscribeRecordService.selectIngByStudentIdAndClassIdList(userId, openClassIdList)
        val bos = openClassIdList.mapNotNull { openClassId ->
            val openClass = openClassList.firstOrNull { it.id == openClassId } ?: return@mapNotNull null
            val teacher = teachers.first { it.id == openClass.teacherId }
            val course = courses.first { it.id == openClass.courseId }
            val courseImg = courseFileList.filter { it.courseId == course.id && it.type == "image" }
            val site = openClass.siteId?.run {
                sites.first { it.id == openClass.siteId }
            }
            val classRecord = classRecords.firstOrNull { it.classId == openClass.id }
            val subscribeRecord = subscribeRecords.firstOrNull { it.classId == openClass.id }
            setOpenClassSimpleInfoVO(openClass, teacher, course, courseImg, site, classRecord, subscribeRecord)
        }
        return Pair(bos, allList.size)
    }

    /**
     * app约课入口
     */
    fun appFilter(dto: AppFilterClassDTO): AppFilterOpenClassVO {
        val userId = currentAppUser().id
        val query = AppFilterOpenClassQuery()
        BeanUtils.copyProperties(dto, query)
        val requestId = dto.requestId ?: ObjectId.createId()
        val allList = openClassService.appFilter(currentAppUser().id, requestId, query)
        val startIndex = (dto.page - 1) * dto.size
        var endIndex = startIndex + dto.size
        if (endIndex > allList.size) {
            endIndex = allList.size
        }
        val openClassIdList = allList.subList(startIndex, endIndex)
        val openClassList = openClassService.selectByIdList(openClassIdList)
        val teacherIds = openClassList.map { it.teacherId }
        val teachers = teacherApi.findByIds(teacherIds)
        val courseIds = openClassList.map { it.courseId }
        val courses = courseService.selectByIdList(courseIds)
        val courseFileList = courseFileService.selectListByCourseIds(courseIds)
        val siteIds = openClassList.mapNotNull { it.siteId }
        val sites = siteService.selectByIdList(siteIds)
        val classRecords = classRecordService.selectSuccessListByStudentIdAndClassIdList(userId, openClassIdList)
        val subscribeRecords = classSubscribeRecordService.selectIngByStudentIdAndClassIdList(userId, openClassIdList)

        val vos = openClassIdList.mapNotNull { openClassId ->
            val openClass = openClassList.firstOrNull { it.id == openClassId } ?: return@mapNotNull null
            val teacher = teachers.first { it.id == openClass.teacherId }
            val course = courses.first { it.id == openClass.courseId }
            val courseImgs = courseFileList.filter { it.courseId == course.id && it.type == "image" }
            val site = openClass.siteId?.run {
                sites.first { it.id == openClass.siteId }
            }
            val classRecord = classRecords.firstOrNull { it.classId == openClass.id }
            val subscribeRecord = subscribeRecords.firstOrNull { it.classId == openClass.id }
            val vo = setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgs, site, classRecord, subscribeRecord)
            //老师详情入口展示课程首图
            if (courseFileList.isNotEmpty()) {
                val courseImg = courseFileList.filter { it.courseId == course.id }.minByOrNull { it.id }
                if (courseImg != null) {
                    vo.teacherPic = courseImg.url
                }
            }
            vo
        }
        return AppFilterOpenClassVO(requestId, ApiPage(dto.page, dto.size, allList.size, vos))
    }

    /**
     * app搜索
     */
    fun appSearch(param: String): ApiPage<AppOpenClassSimpleInfoVO> {
        val userId = currentAppUser().id
        val result = openClassService.appSearch(param)
        val openClassList = result.records
        val teacherIds = openClassList.map { it.teacherId }
        val teachers = teacherApi.findByIds(teacherIds)
        val courseIds = openClassList.map { it.courseId }
        val courses = courseService.selectByIdList(courseIds)
        val courseFileList = courseFileService.selectListByCourseIds(courseIds)
        val siteIds = openClassList.mapNotNull { it.siteId }
        val sites = siteService.selectByIdList(siteIds)
        val openClassIds = openClassList.map { it.id }
        val classRecords = classRecordService.selectSuccessListByStudentIdAndClassIdList(userId, openClassIds)
        val subscribeRecords = classSubscribeRecordService.selectIngByStudentIdAndClassIdList(userId, openClassIds)

        val vos = result.records.map { openClass ->
            val teacher = teachers.first { it.id == openClass.teacherId }
            val course = courses.first { it.id == openClass.courseId }
            val courseImgs = courseFileList.filter { it.courseId == course.id && it.type == "image" }
            val site = openClass.siteId?.run {
                sites.first { it.id == openClass.siteId }
            }
            val classRecord = classRecords.firstOrNull { it.classId == openClass.id }
            val subscribeRecord = subscribeRecords.firstOrNull { it.classId == openClass.id }
            setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgs, site, classRecord, subscribeRecord)
        }
        return result.build(vos)
    }

    /**
     * app课程详情
     */
    fun appDetailInfo(openClassId: Int): AppOpenClassDetailInfoVO {
        val userId = currentAppUser().id
        val openClass = openClassService.getById(openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        val course = courseService.getById(openClass.courseId)!!
        val courseFiles = courseFileService.selectListByCourseId(course.id)
        val site = openClass.siteId?.run {
            siteService.getById(this)
        }
        val danceRoom = openClass.danceRoomId?.run {
            danceRoomService.getById(this)
        }
        val teacher = teacherApi.findById(openClass.teacherId)
        //已预约的记录
        val recordList = classRecordService.selectSuccessListByClassId(openClassId)
        val classRecord = recordList.firstOrNull { it.studentId == userId }
        val subscribeRecord = classSubscribeRecordService.selectIngByStudentIdAndClassId(userId, openClassId)
        return setOpenClassDetailInfoVO(userId, openClass, teacher, course, courseFiles, site, danceRoom, classRecord, recordList, subscribeRecord)
    }

    private fun setOpenClassSimpleInfoVO(
        openClass: OpenClass,
        teacher: TeacherBO,
        course: Course,
        courseImgs: List<CourseFile>,
        site: Site?,
        classRecord: ClassRecord?,
        subscribeRecord: ClassSubscribeRecord?
    ): AppOpenClassSimpleInfoVO {
        val vo = AppOpenClassSimpleInfoVO()
        vo.id = openClass.id
        vo.pic = courseImgs.filter { it.type == "image" }.minByOrNull { it.id }?.url ?: ""
        vo.teacherName = teacher.nickname
        vo.teacherPic = teacher.pic
        vo.evaluateScore = course.evaluateScore
        vo.courseName = course.name
        vo.danceType = course.danceType
        vo.difficulty = course.difficulty
        vo.ageRange = course.ageRange
        vo.attendClassDay = openClass.attendClassTime.toLocalDate()
        vo.attendClassTime = openClass.attendClassTime.toLocalTime()
        vo.finishClassTime = openClass.finishClassTime.toLocalTime()
        vo.attendClassWeek = openClass.attendClassTime.dayOfWeek.value
        vo.atLatestCancelTime = openClass.atLatestCancelTime
        vo.siteName = openClass.customSiteName ?: site!!.name
        vo.price = openClass.price
        vo.memberPrice = openClass.memberPrice
        vo.advanceBookingHours = openClass.advanceBookingHours
        vo.desc = course.desc
        vo.number = openClass.number
        vo.minOpenClassNumber = openClass.minRecordCount
        vo.surplusNumber = openClass.number - openClass.recordCount
        vo.subscribeNumber = openClass.subscribeCount
        if (openClass.status == OpenClassStatus.PENDING) {
            vo.status = when {
                classRecord != null && classRecord.status != ClassRecordStatus.CANCEL                  -> 1
                subscribeRecord != null && subscribeRecord.status != ClassSubscribeRecordStatus.CANCEL -> 3
                else                                                                                   -> {
                    if (openClass.recordCount < openClass.number) 0 else 2
                }
            }
        } else {
            vo.status = 4
        }
        vo.courseId = openClass.courseId
        vo.siteId = openClass.siteId
        vo.danceRoomId = openClass.danceRoomId
        return vo
    }

    private fun setOpenClassDetailInfoVO(
        userId: Int,
        openClass: OpenClass,
        teacher: TeacherBO,
        course: Course,
        courseImgs: List<CourseFile>,
        site: Site?,
        danceRoom: DanceRoom?,
        classRecord: ClassRecord?,
        recordList: List<ClassRecord>,
        subscribeRecord: ClassSubscribeRecord?,
    ): AppOpenClassDetailInfoVO {
        val simpleInfoVO = setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgs, site, classRecord, subscribeRecord)
        val detailInfoVO = AppOpenClassDetailInfoVO()
        BeanUtils.copyProperties(simpleInfoVO, detailInfoVO)
        detailInfoVO.imgs = courseImgs.filter { it.type == "image" }.map { it.url }
        detailInfoVO.videos = courseImgs.filter { it.type == "video" }.map { it.url }
        detailInfoVO.evaluateTotalCount = course.evaluateTotalCount
        detailInfoVO.number = openClass.number
        detailInfoVO.danceRoomName = danceRoom?.name ?: openClass.customDanceRoomName ?: ""
        detailInfoVO.siteAddress = openClass.customSiteAddress?.run {
            "${openClass.customSiteProvinceAndCity}${openClass.customSiteCounty} $this"
        } ?: "${site!!.province}${site.city}${site.county ?: ""}${site.address}"
        detailInfoVO.teacherInfo = teacherApi.findClassDetailTeacherInfoVOById(openClass.teacherId, userId)
        detailInfoVO.selectedIndexList = recordList.map { AppOpenClassDetailInfoIndexVO(it.rowIndex, it.columIndex) }
        detailInfoVO.selfRowIndex = classRecord?.rowIndex
        detailInfoVO.selfColumnIndex = classRecord?.columIndex
        detailInfoVO.subscribeNo = subscribeRecord?.number
        detailInfoVO.theFrontCount = subscribeRecord?.number?.run {
            classSubscribeRecordService.theFrontCountByNumber(openClass.id, this)
        }
        detailInfoVO.openGroup = openClass.allowGroup
        detailInfoVO.columnNumber = openClass.columnNumber
        detailInfoVO.rowNumber = openClass.rowNumber
        detailInfoVO.classRecordId = classRecord?.id
        detailInfoVO.classRecordSubscribeId = subscribeRecord?.id
        detailInfoVO.bookingRestrictions = openClass.bookingRestrictions
        return detailInfoVO
    }

    /**
     * app约课下单
     */
    @Transactional(rollbackFor = [Exception::class])
    fun recordPushOrder(dto: AppClassRecordPushOrderDTO): AppClassRecordPushOrderVO {
        val userId = currentAppUser().id
        val openClass = openClassService.getById(dto.openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        val course = courseService.getById(openClass.courseId)

        //检查状态
        classRecordService.pushOrderCheckStatus(openClass.convertToBO()!!, dto.rowIndex, dto.columnIndex)

        val businessOrderId = ObjectId.createId()
        //锁定位置
        val classRecord = classRecordService.pushOrder(userId, openClass.id, businessOrderId, dto.rowIndex, dto.columnIndex)

        //下单
        val (businessOrder, weiXinPayInfo) = classPushOrder(userId, businessOrderId, openClass, course, 1, classRecord.id, dto.periodCardId, dto.userCouponId)

        if (businessOrder.state == BusinessOrderState.PLACE_ORDER) {
            classCommonAppService.recordClassPaySuccess(classRecord, openClass, businessOrder.orderMoney)
            groupAbout(userId, openClass, 1)
        }

        return AppClassRecordPushOrderVO().apply {
            orderId = businessOrderId
            state = businessOrder.state
            payInfo = weiXinPayInfo
            recordId = classRecord.id
        }
    }

    /**
     * app预约排队下单
     */
    @Transactional(rollbackFor = [Exception::class])
    fun subscribeRecordPushOrder(openClassId: Int): AppClassRecordPushOrderVO {
        val userId = currentAppUser().id
        val openClass = openClassService.getById(openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        val course = courseService.getById(openClass.courseId)

        //检查是否能进行排队预约
        classSubscribeRecordService.pushOrderCheckStatus(userId, openClass.convertToBO()!!)

        val businessOrderId = ObjectId.createId()
        //入队预约
        val subscribeRecord = classSubscribeRecordService.pushOrder(userId, openClass.id, businessOrderId)

        //下单
        val (businessOrder, weiXinPayInfo) = classPushOrder(userId, businessOrderId, openClass, course, 2, subscribeRecord.id, null, null)
        if (businessOrder.state == BusinessOrderState.PLACE_ORDER) {
            subscribeRecordClassPaySuccess(openClass.id)
            groupAbout(userId, openClass, 2)
        }

        return AppClassRecordPushOrderVO().apply {
            orderId = businessOrderId
            state = businessOrder.state
            payInfo = weiXinPayInfo
            subscribeNo = subscribeRecord.number
            recordId = subscribeRecord.id
        }
    }

    private fun classPushOrder(
        userId: Int,
        businessOrderId: String,
        openClass: OpenClass,
        course: Course,
        pushOrderType: Int,
        recordId: Int,
        periodCardId: Long?,
        userCouponId: Long?
    ): Pair<BusinessOrder, PrepayWithRequestPaymentResponse?> {
        val user = userApi.cacheUserInfo(userId)
        val teacher = teacherApi.findById(openClass.teacherId)
        var payType = BusinessOrderPayType.COIN
        if (user.walletBO.coin < openClass.price) {
            payType = BusinessOrderPayType.MIX
        }
        if (user.walletBO.coin.compareTo(BigDecimal.ZERO) == 0) {
            payType = BusinessOrderPayType.CASH
        }

        // 直接获取年卡信息，通过年卡信息是否存在来判断会员状态
        val annualCard = cardCourseApi.getAnnualCard(userId.toLong())
        val isVip = annualCard != null
        
        //付费项逻辑
        // 如果用户是会员，使用会员价；否则使用普通价格
        var payPrice = if (isVip && openClass.memberPrice != null && openClass.memberPrice > BigDecimal.ZERO) {
            openClass.memberPrice
        } else {
            openClass.price
        }
        val paymentItemList = mutableListOf<BusinessOrderPaymentItem>()
        
        // 如果用户是会员，记录年卡使用情况
        if (isVip && annualCard != null) {
            // 记录年卡使用
            cardCourseApi.useYearCard(userId.toLong(), annualCard, businessOrderId, openClass.id.toLong(), openClass.memberPrice)
        }
        
        // 处理期限卡支付
        if (periodCardId != null) {
            // 将int类型的userId转换为Long类型
            val userIdLong = userId.toLong()
            // 使用期限卡
            cardCourseApi.usePeriodCard(userIdLong, periodCardId, annualCard, businessOrderId, openClass.id.toLong())
            
            // 添加支付项
            paymentItemList.add(
                BusinessOrderPaymentItem(
                    payType = BusinessOrderPaymentType.PERIOD_CARD,
                    type = 1,
                    price = payPrice, // 记录原价
                    businessOrderId = businessOrderId
                )
            )
            // 期限卡支付后价格为0
            payPrice = BigDecimal.ZERO
            
            // 使用期限卡后，设置支付类型为期限卡
            payType = BusinessOrderPayType.PERIOD_CARD
        } 
        // 处理优惠券支付（只有在不使用期限卡时才考虑优惠券）
        else if (userCouponId != null && payPrice.compareTo(BigDecimal.ZERO) > 0) {
            // 将int类型的userId转换为Long类型
            val userIdLong = userId.toLong()
            // 获取优惠券面值
            val couponFaceValue = cardCourseApi.getCouponFaceValue(userIdLong, userCouponId)
            // 使用优惠券
            cardCourseApi.useCoupon(userIdLong, userCouponId, annualCard, businessOrderId, openClass.id.toLong())
            
            // 优惠券不作为支付项，只在订单的businessParam中记录
            
            // 减去优惠券面值
            payPrice = payPrice.subtract(couponFaceValue)
            if (payPrice.compareTo(BigDecimal.ZERO) < 0) {
                payPrice = BigDecimal.ZERO
            }
        }
        
        // 处理余额支付（只有在价格大于0时才考虑）
        if (payPrice.compareTo(BigDecimal.ZERO) > 0) {
            if (payType == BusinessOrderPayType.COIN || payType == BusinessOrderPayType.MIX) {
                val costCoin = user.walletBO.coin.min(payPrice)
                if (costCoin > BigDecimal.ZERO) {
                    userApi.costCoin(userId, costCoin, UserWalletLogType.BUY_CLASS, "预约课程：${course.name}", openClass.teacherId, businessOrderId)
                    paymentItemList.add(
                        BusinessOrderPaymentItem(
                            payType = BusinessOrderPaymentType.COIN,
                            type = 1,
                            price = costCoin,
                            businessOrderId = businessOrderId
                        )
                    )
                    payPrice = payPrice.subtract(costCoin)
                }
            }
        }
        
        // 处理微信支付（只有在价格大于0时才考虑）
        var weiXinPayInfo: PrepayWithRequestPaymentResponse? = null
        if (payPrice.compareTo(BigDecimal.ZERO) > 0 && (payType == BusinessOrderPayType.CASH || payType == BusinessOrderPayType.MIX)) {
            val outTradeNo = idGenerator.getOutTradeNo()
            weiXinPayInfo = weiXinService.jsApiPay(user.openId, outTradeNo, payPrice, "购买课程-${payPrice}元")
            thirdPayOrderService.pushOrder(
                outTradeNo,
                payPrice,
                userId,
                ThirdPayBusinessType.BUY_CLASS,
                mapOf("business_order_id" to businessOrderId, "push_order_type" to pushOrderType)
            )
        }
        
        //创建业务订单
        val businessOrder = BusinessOrder(
            id = businessOrderId,
            type = BusinessOrderType.BUY_CLASS,
            userId = user.id,
            toId = openClass.teacherId,
            businessId = openClass.id,
            businessParam = JacksonUtil.toJson(mapOf(
                "push_order_type" to pushOrderType, 
                "course_name" to course.name,
                "period_card_id" to periodCardId,
                "user_coupon_id" to userCouponId,
                "annual_card_id" to annualCard?.id
            )),
            recordId = recordId,
            orderMoney = payPrice,
            settleRatio = teacher.settleRatio,
            payType = payType,
            entryIntoForceTime = openClass.attendClassTime
        )
        
        // 如果使用期限卡或者价格为0，则直接完成订单
        if (payType == BusinessOrderPayType.PERIOD_CARD || payPrice.compareTo(BigDecimal.ZERO) == 0) {
            businessOrder.state = BusinessOrderState.PLACE_ORDER
        }
        
        businessOrderService.createOrder(businessOrder, paymentItemList)

        return businessOrder to weiXinPayInfo
    }

    /**
     * 拼团操作相关
     */
    fun groupAbout(userId: Int, openClass: OpenClass, pushOrderType: Int) {
        if (!openClass.allowGroup) return

        //查看在此开课课程下是否参与拼团
        val joinGroup = classGroupMemberService.lastJoinGroupByClassId(userId, openClass.id)
        if (joinGroup?.state == 1) return
        joinGroup?.run joinGroup@{
            val groupInfo = classGroupInfoService.getById(groupId)
            if (groupInfo.successCount >= 5) return@joinGroup

            //成功拼团
            val successJoin = classGroupInfoService.successJoinGroup(groupId)
            if (!successJoin) return@joinGroup
            classGroupMemberService.successJoinGroup(groupId, userId)
            return
        }

        if (pushOrderType != 1) return

        //未参与拼团或者其他拼团已满，重新开团
        val groupId = classGroupInfoService.openGroup(userId, openClass.id)
        classGroupMemberService.successJoinGroup(groupId, openClass.id, userId)
    }

    @EventListener(ThirdPayOrderSuccessEvent::class)
    fun thirdPayOrderSuccess(event: ThirdPayOrderSuccessEvent) {
        event.apply {
            if (businessType != ThirdPayBusinessType.BUY_CLASS) return

            val businessParamData = JacksonUtil.toObject(businessParam!!)
            val businessOrderId = businessParamData["business_order_id"] as String
            val pushOrderType = businessParamData["push_order_type"] as Int

            val businessOrder = businessOrderService.getById(businessOrderId)
            if (pushOrderType == 1) {
                //约课成功
                val openClass = openClassService.getById(businessOrder.businessId)
                val classRecord = classRecordService.getById(businessOrder.recordId)
                classCommonAppService.recordClassPaySuccess(classRecord, openClass, businessOrder.orderMoney)
            } else {
                //排队成功
                subscribeRecordClassPaySuccess(businessOrder.businessId)
            }

            val openClass = openClassService.getById(businessOrder.businessId)
            groupAbout(businessOrder.userId, openClass, pushOrderType)
        }
    }

    @EventListener(ThirdPayOrderTimeOutEvent::class)
    @Transactional(rollbackFor = [Exception::class])
    fun thirdPayOrderTimeOut(event: ThirdPayOrderTimeOutEvent) {
        kotlin.runCatching {
            event.apply {
                if (businessType != ThirdPayBusinessType.BUY_CLASS) return

                val businessParamData = JacksonUtil.toObject(businessParam!!)
                val businessOrderId = businessParamData["business_order_id"] as String
                val pushOrderType = businessParamData["push_order_type"] as Int

                val businessOrder = businessOrderService.getById(businessOrderId)
                if (pushOrderType == 1) {
                    appCancelPayClassRecord(businessOrder.recordId)
                } else if (pushOrderType == 2) {
                    appCancelPayClassRecordSubscribe(businessOrder.recordId)
                }
            }
        }
    }

    /**
     * 排队支付成功
     */
    private fun subscribeRecordClassPaySuccess(openClassId: Int) {
        //增加排队人数
        openClassService.addSubscribeCount(openClassId)
    }

    fun teacherOpenClassPendingCount(teacherIdList: List<Int>): Map<Int, Int> {
        return openClassService.teacherOpenClassPendingCount(teacherIdList)
    }

    fun teacherClassRecordCount(teacherIdList: List<Int>): Map<Int, Int> {
        return classRecordService.teacherClassRecordCount(teacherIdList)
    }

    /**
     * app约课记录
     */
    fun appClassRecordList(dto: AppClassRecordListDTO): AppClassRecordListVO {
        //查询约课、排队记录对应的课程id
        val query = AppClassRecordListQuery()
        BeanUtils.copyProperties(dto, query)
        val userId = currentAppUser().id
        query.studentId = userId
        val requestId = dto.requestId ?: ObjectId.createId()
        val allList = classRecordCommonService.appClassRecordList(userId, requestId, query)
        val startIndex = (dto.page - 1) * dto.size
        var endIndex = startIndex + dto.size
        if (endIndex > allList.size) {
            endIndex = allList.size
        }
        val pageIdList = allList.subList(startIndex, endIndex)
        val openClassList = openClassService.selectByIdList(pageIdList)

        //封装各个信息
        val teacherIdList = openClassList.map { it.teacherId }
        val teacherList = teacherApi.findByIds(teacherIdList)
        val courseIdList = openClassList.map { it.courseId }
        val courseList = courseService.selectByIdList(courseIdList)
        val courseFileList = courseFileService.selectListByCourseIds(courseIdList)
        val siteIdList = openClassList.mapNotNull { it.siteId }
        val siteList = siteService.selectByIdList(siteIdList)
        val classRecordList = classRecordService.selectByStudentIdAndClassIdList(userId, pageIdList)
        val subscribeRecordList = classSubscribeRecordService.selectByStudentIdAndClassIdList(userId, pageIdList)
        val businessOrderIdList = listOf(
            classRecordList.map { it.orderId },
            subscribeRecordList.map { it.orderId }
        ).flatten()
        val businessOrderPaymentList = businessOrderService.findPaymentItemListByIdList(businessOrderIdList)

        val vos = pageIdList.map { openClassId ->
            val openClass = openClassList.first { it.id == openClassId }
            val teacher = teacherList.first { it.id == openClass.teacherId }
            val course = courseList.first { it.id == openClass.courseId }
            val courseImgs = courseFileList.filter { it.courseId == openClass.courseId && it.type == "image" }
            val site = siteList.find { it.id == openClass.siteId }
            val voAc = { classRecord: ClassRecord?, subscribeRecord: ClassSubscribeRecord? ->
                val simpleInfoVO = setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgs, site, classRecord, subscribeRecord)
                val vo = AppClassRecordListInfoVO()
                vo.recordId = classRecord?.id ?: subscribeRecord?.id ?: 0
                vo.classRecordId = classRecord?.id
                vo.classRecordSubscribeId = subscribeRecord?.id
                BeanUtils.copyProperties(simpleInfoVO, vo)

                //约课状态
                //待上课
                if (openClass.status == OpenClassStatus.PENDING) {
                    vo.listStatus = 0
                }
                //排队中
                if (subscribeRecord?.status == ClassSubscribeRecordStatus.ING) {
                    vo.listStatus = 1
                }
                //已上课
                if (openClass.status == OpenClassStatus.ING || openClass.status == OpenClassStatus.FINISH) {
                    vo.listStatus = 2
                }
                //已解散
                if (openClass.status == OpenClassStatus.CANCEL) {
                    vo.listStatus = 3
                }
                //已退课
                if (subscribeRecord?.status == ClassSubscribeRecordStatus.CANCEL || classRecord?.status == ClassRecordStatus.CANCEL) {
                    vo.listStatus = 4
                }

                vo.selfRowIndex = classRecord?.rowIndex
                vo.selfColumnIndex = classRecord?.columIndex
                vo.theFrontCount = subscribeRecord?.number?.run {
                    classSubscribeRecordService.theFrontCountByNumber(openClass.id, this)
                }
                if (vo.listStatus == 3) {
                    vo.showDesc = "解散原因：${openClass.cancelReason}"
                }
                if (vo.listStatus == 4) {
                    val businessOrderId = classRecord?.orderId ?: subscribeRecord!!.orderId
                    val refundList = businessOrderPaymentList.filter { it.businessOrderId == businessOrderId && it.type == -1 }
                    if (refundList.isNotEmpty()) {
                        vo.showDesc = "退款时间：${refundList.minOf { it.createTime }.format(LOCAL_DATE_TIME_FORMATTER)}"
                        vo.refundPrice = refundList.sumOf { it.price }.abs()
                    }
                }
                
                // 获取实付金额
                val businessOrderId = classRecord?.orderId ?: subscribeRecord?.orderId
                if (businessOrderId != null) {
                    // 获取实付金额
                    val businessOrder = businessOrderService.getById(businessOrderId)
                    if (businessOrder != null) {
                        vo.realPayPrice = businessOrder.orderMoney
                    }
                }
                
                vo.progress = openClass.recordCount
                vo
            }
            val thisClassRecordList = classRecordList.filter { it.classId == openClass.id }
            val classRecordVO = thisClassRecordList.mapNotNull { classRecord ->
                if (dto.statusList?.isNotEmpty() == true) {
                    if (dto.statusList!!.none { it == 4 } && classRecord.status == ClassRecordStatus.CANCEL) {
                        return@mapNotNull null
                    }
                }
                voAc(classRecord, null)
            }
            val thisSubscribeRecordList = subscribeRecordList.filter { it.classId == openClass.id && it.status != ClassSubscribeRecordStatus.SUCCESS }
            val subscribeRecordVO = thisSubscribeRecordList.map { subscribeRecord ->
                voAc(null, subscribeRecord)
            }
            listOf(classRecordVO, subscribeRecordVO).flatten()
        }.flatten()
        return AppClassRecordListVO(requestId, ApiPage(dto.page, dto.size, allList.size, vos))
    }

    /**
     * app约课详情
     */
    fun appClassRecordDetailInfo(recordId: Int, source: Int): AppClassRecordDetailInfoVO {
        val userId = currentAppUser().id
        val classRecord = if (source == 1) classRecordService.getById(recordId) else null
        val subscribeRecord = if (source == 2) classSubscribeRecordService.getById(recordId) else null
        if (classRecord == null && subscribeRecord == null) throw BizException(BizExceptionConst.CUSTOM_ERROR, "约课记录不存在")
        val openClassId = classRecord?.classId ?: subscribeRecord!!.classId
        val openClass = openClassService.getById(openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        val course = courseService.getById(openClass.courseId)!!
        val courseFiles = courseFileService.selectListByCourseId(course.id)
        val site = openClass.siteId?.run {
            siteService.getById(this)
        }
        val danceRoom = openClass.danceRoomId?.run {
            danceRoomService.getById(this)
        }
        val teacher = teacherApi.findById(openClass.teacherId)
        //已预约的记录
        val recordList = classRecordService.selectSuccessListByClassId(openClass.id)
        val classDetailInfoVO = setOpenClassDetailInfoVO(
            userId,
            openClass,
            teacher,
            course,
            courseFiles,
            site,
            danceRoom,
            classRecord,
            recordList,
            subscribeRecord
        )
        val classRecordDetailInfoVO = AppClassRecordDetailInfoVO()
        classRecordDetailInfoVO.recordId = classRecord?.id ?: subscribeRecord?.id ?: 0
        classRecordDetailInfoVO.classRecordId = classRecord?.id
        classRecordDetailInfoVO.classRecordSubscribeId = subscribeRecord?.id
        BeanUtils.copyProperties(classDetailInfoVO, classRecordDetailInfoVO)
        //待上课
        if (openClass.status == OpenClassStatus.PENDING) {
            classRecordDetailInfoVO.classStatus = 0
        }
        //排队中
        if (subscribeRecord?.status == ClassSubscribeRecordStatus.ING) {
            classRecordDetailInfoVO.classStatus = 1
        }
        //已上课
        if (openClass.status == OpenClassStatus.ING || openClass.status == OpenClassStatus.FINISH) {
            classRecordDetailInfoVO.classStatus = 2
        }
        //已解散
        if (openClass.status == OpenClassStatus.CANCEL) {
            classRecordDetailInfoVO.classStatus = 3
        }
        //已退课
        if (subscribeRecord?.status == ClassSubscribeRecordStatus.CANCEL || classRecord?.status == ClassRecordStatus.CANCEL) {
            classRecordDetailInfoVO.classStatus = 4
        }

        val fileList = openClassFileService.findAllByClassId(openClassId)
        classRecordDetailInfoVO.videoList = fileList.filter { it.type == "video" }.map { it.url }
        classRecordDetailInfoVO.imgList = fileList.filter { it.type == "image" }.map { it.url }

        val orderId = (classRecord?.orderId ?: subscribeRecord?.orderId)!!
        val businessOrder = businessOrderService.getById(orderId)
        val businessOrderPayList = businessOrderService.findPaymentItemList(orderId)
        classRecordDetailInfoVO.realPayPrice = businessOrder.orderMoney
        classRecordDetailInfoVO.pushOrderTime = businessOrder.createTime
        classRecordDetailInfoVO.payGroup = businessOrderPayList.filter { it.type == 1 }.joinToString(" + ") { "￥${it.price}${it.payType.desc}" }

        val evaluateInfo = evaluateRecordService.findInfoByUserIdAndTargetIdAndRelationId(userId, course.id, "${openClass.id}")
        classRecordDetailInfoVO.hasEvaluate = evaluateInfo != null
        evaluateInfo?.run {
            classRecordDetailInfoVO.selfEvaluateScore = this.score
            classRecordDetailInfoVO.selfEvaluateContent = this.content
            classRecordDetailInfoVO.selfEvaluateTime = this.createTime
        }

        classRecordDetailInfoVO.cancelReason = openClass.cancelReason
        classRecordDetailInfoVO.refundReason = businessOrder.refundedReason
        val refundList = businessOrderPayList.filter { it.type == -1 }
        classRecordDetailInfoVO.refundPrice = refundList.sumOf { it.price }.abs()
        classRecordDetailInfoVO.refundMode = refundList.joinToString(" + ") { "￥${it.price}${it.payType.desc}" }
        classRecordDetailInfoVO.refundTime = businessOrder.refundedTime

        return classRecordDetailInfoVO
    }

    /**
     * app评价课程跟老师
     */
    @Transactional(rollbackFor = [Exception::class])
    fun appEvaluateClassAndTeacher(dto: AppEvaluateClassDTO) {
        val userId = currentAppUser().id
        val openClass = openClassService.getById(dto.openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")

        //评价记录入库
        val recordList = mutableListOf<EvaluateRecord>()
        val classRecord = EvaluateRecord(
            score = dto.classScore,
            content = dto.classContent,
            targetId = openClass.courseId,
            userId = userId,
            type = EvaluateType.CLASS,
            relationId = "${dto.openClassId}"
        )
        recordList.add(classRecord)
        val teacherRecord = EvaluateRecord(
            score = dto.teacherScore,
            content = dto.teacherContent,
            targetId = openClass.teacherId,
            userId = userId,
            type = EvaluateType.TEACHER,
            relationId = "${dto.openClassId}"
        )
        recordList.add(teacherRecord)
        evaluateRecordService.saveBatch(recordList)

        //回写开课信息、课程信息评价
        openClassService.addEvaluate(openClass, dto.classScore)
        val course = courseService.getById(openClass.courseId)!!
        courseService.addEvaluate(course, dto.classScore)

        //回写老师信息评价
        teacherApi.addEvaluate(openClass.teacherId, dto.teacherScore)
    }

    /**
     * app取消约课
     */
    @Transactional(rollbackFor = [Exception::class])
    fun appCancelClassRecord(classRecordId: Int): List<AppClassRecordCancelVO> {
        val classRecord = classRecordService.getById(classRecordId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "约课信息不存在")
        if (classRecord.status != ClassRecordStatus.SUCCESS) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该约课信息不可取消")
        }
        val openClass = openClassService.getById(classRecord.classId)
        if (openClass.status != OpenClassStatus.PENDING) throw BizException(BizExceptionConst.CUSTOM_ERROR, "已上课，无法取消预约")
        if (openClass.atLatestCancelTime <= LocalDateTime.now()) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "老师已经开始备课，无法退课，具体规则请见「课程须知」。如有问题请联系客服")
        }

        val (refundCoin, refundWeiXin, result) = classCommonAppService.cancelClassRecord(
            classRecord,
            openClass,
            "用户取消约课",
            checkRecordStatus = ClassRecordStatus.SUCCESS
        )

        val course = courseService.getById(openClass.courseId)
        platformInformService.add(
            "${course.name}课程退款已到账：D.Base钱包到账金币${refundCoin}，微信支付退款${refundWeiXin}，请注意查收",
            classRecord.studentId
        )
        val student = userApi.getUserById(classRecord.studentId)!!
        platformInformService.add(
            "${student.nickname}已退课：${course.name} ${openClass.attendClassTime.format(LOCAL_DATE_TIME_MINUTE_FORMATTER)}~${
                openClass.finishClassTime.format(LOCAL_TIME_MINUTE_FORMATTER)
            }",
            classRecord.studentId
        )

        //查看该开课是否有排队中的用户
        classSubscribeRecordService.firstQueueInInfo(openClass.id)?.run {
            Thread.ofVirtual().start {
                Thread.sleep(1000)
                classCommonAppService.classSubscribeToRecord(classRecord, this, openClass)
            }
        }
        return result
    }


    /**
     * app取消排队
     */
    @Transactional(rollbackFor = [Exception::class])
    fun appCancelClassRecordSubscribe(classRecordSubscribeId: Int): List<AppClassRecordCancelVO> {
        val classRecordSubscribe = classSubscribeRecordService.getById(classRecordSubscribeId) ?: throw BizException(
            BizExceptionConst.CUSTOM_ERROR,
            "排队约课信息不存在"
        )
        if (classRecordSubscribe.status != ClassSubscribeRecordStatus.ING) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该排队约课信息不可取消")
        }
        val openClass = openClassService.getById(classRecordSubscribe.classId)
        val course = courseService.getById(openClass.courseId)

        //修改排队约课状态
        classSubscribeRecordService.cancel(classRecordSubscribe.id)
        //修改开课信息排队人数
        openClassService.minSubscribeCount(classRecordSubscribe.classId)
        //退出拼团
        classCommonAppService.cancelSuccessGroup(classRecordSubscribe.classId, classRecordSubscribe.studentId)

        //执行订单退款
        val result = classCommonAppService.cancelClassRefundSubscribe(classRecordSubscribe, "用户取消排队约课").map {
            AppClassRecordCancelVO(it.payType, it.price)
        }
        val refundCoin = result.firstOrNull { it.payType == BusinessOrderPaymentType.COIN }?.money ?: BigDecimal.ZERO
        val refundWeiXin = result.firstOrNull { it.payType == BusinessOrderPaymentType.WEI_XIN }?.money ?: BigDecimal.ZERO
        platformInformService.add(
            "${course.name}课程解散退款已到账：D.Base钱包到账金币${refundCoin}，微信支付退款${refundWeiXin}，请注意查收",
            classRecordSubscribe.studentId
        )

        return result
    }

    @Transactional(rollbackFor = [Exception::class])
    fun appCancelPayClassRecord(classRecordId: Int) {
        val classRecord = classRecordService.getById(classRecordId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "约课信息不存在")
        if (classRecord.status != ClassRecordStatus.SUCCESS) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该约课信息不可取消")
        }
        val openClass = openClassService.getById(classRecord.classId)
        if (openClass.status != OpenClassStatus.PENDING) throw BizException(BizExceptionConst.CUSTOM_ERROR, "已上课，无法取消预约")

        //删除订单
        businessOrderCommonAppService.removeOrderInfo(classRecord.orderId, "用户约课取消支付")

        //删除约课记录
        classRecordService.removeById(classRecord.id)

        //查看该开课是否有排队中的用户
        classSubscribeRecordService.firstQueueInInfo(openClass.id)?.run {
            Thread.ofVirtual().start {
                Thread.sleep(1000)
                classCommonAppService.classSubscribeToRecord(classRecord, this, openClass)
            }
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    fun appCancelPayClassRecordSubscribe(classRecordSubscribeId: Int) {
        val classRecordSubscribe = classSubscribeRecordService.getById(classRecordSubscribeId) ?: throw BizException(
            BizExceptionConst.CUSTOM_ERROR,
            "排队约课信息不存在"
        )
        if (classRecordSubscribe.status != ClassSubscribeRecordStatus.ING) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该排队约课信息不可取消")
        }

        //删除订单
        businessOrderCommonAppService.removeOrderInfo(classRecordSubscribe.orderId, "用户排队约课取消支付")

        //删除排队记录
        classSubscribeRecordService.removeById(classRecordSubscribe.id)
        redissonClient.getQueue<Int>(BizRedisKey.OPEN_CLASS_SUBSCRIBE_QUEUE(classRecordSubscribe.classId)).remove(classRecordSubscribe.id)
    }

    /**
     * 开课课程变成已上课
     */
    @EventListener(OpenClassIngEvent::class)
    fun openClassToIngEvent(event: OpenClassIngEvent) {
        event.apply {
            //将未排队成功的用户进行退款
            val pollAc = {
                redissonClient.getQueue<Int>(BizRedisKey.OPEN_CLASS_SUBSCRIBE_QUEUE(classId)).poll()
            }
            var lastSubscribeId: Int?
            while (pollAc().apply { lastSubscribeId = this } != null) {
                val classRecordSubscribe = classSubscribeRecordService.getById(lastSubscribeId!!)!!
                //修改排队约课状态
                val success = classSubscribeRecordService.subscribeFail(classRecordSubscribe)
                if (success) {
                    //执行订单退款
                    val refundList = classCommonAppService.cancelClassRefundSubscribe(classRecordSubscribe, "排队超时").associate { it.payType to it.price }
                    val openClass = openClassService.getById(classRecordSubscribe.classId)
                    val course = courseService.getById(openClass.courseId)
                    platformInformService.add(
                        "很抱歉通知您，${course.name}课程已开始上课，您仍处于排队中，未能约上课程，已向您全额退款：：D.Base钱包到账金币${refundList[BusinessOrderPaymentType.COIN] ?: 0}，微信支付退款${refundList[BusinessOrderPaymentType.WEI_XIN] ?: 0}，请注意查收",
                        classRecordSubscribe.studentId
                    )
                }
            }
        }
    }

    /**
     * app已上课申请取消约课
     */
    @Transactional(rollbackFor = [Exception::class])
    fun appApplyCancelClassRecord(dto: AppClassRecordApplyCancelDTO): List<AppClassRecordCancelVO> {
        val classRecord = classRecordService.getById(dto.classRecordId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "约课信息不存在")
        if (classRecord.status != ClassRecordStatus.FINISH) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该约课信息不可取消")
        }
        val openClass = openClassService.getById(classRecord.classId)
        if (openClass.hasSettle) {
            throw BizException(BizExceptionConst.CUSTOM_ERROR, "该课程已结算，不可取消")
        }

        //申请记录入库
        val log = ClassRecordApplyCancel(
            classRecordId = classRecord.id,
            openClassId = openClass.id,
            studentId = currentAppUser().id,
            applyCancelReason = dto.applyCancelReason,
            imgs = dto.imgs.joinToString(","),
            businessOrderId = classRecord.orderId
        )
        classRecordApplyCancelService.save(log)

        //查看付款信息
        return businessOrderService.findPaymentItemList(classRecord.orderId).map {
            AppClassRecordCancelVO(
                it.payType,
                it.price
            )
        }
    }

    /**
     * app开课记录
     */
    fun appOpenClassRecord(dto: AppOpenClassRecordListDTO): List<AppOpenClassRecordListVO> {
        val allList = openClassService.appOpenClassRecord(currentAppUser().id, dto.date, dto.name)
        val courseIdList = allList.map { it.courseId }
        val allCourseList = courseService.selectByIdList(courseIdList)
        val allCourseFileList = courseFileService.selectListByCourseIds(courseIdList)
        val teacherIdList = allList.map { it.teacherId }
        val allTeacherList = teacherApi.findByIds(teacherIdList)
        val siteIdList = allList.mapNotNull { it.siteId }
        val allSiteList = siteService.selectByIdList(siteIdList)
        val allApplyCancelList = classRecordApplyCancelService.selectListByClassIdList(allList.map { it.id })

        return allList.map { openClass ->
            val course = allCourseList.first { it.id == openClass.courseId }
            val courseImgList = allCourseFileList.filter { it.courseId == course.id && it.type == "image" }
            val teacher = allTeacherList.first { it.id == openClass.teacherId }
            val site = allSiteList.firstOrNull { it.id == openClass.siteId }
            val simpleInfoVO = setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgList, site, null, null)
            val vo = AppOpenClassRecordListVO()
            BeanUtils.copyProperties(simpleInfoVO, vo)
            vo.totalMoney = openClass.totalMoney
            vo.attendClassTimestamp = openClass.attendClassTime.toTimestamp()
            vo.classStatus = openClass.status
            vo.number = openClass.number
            vo.progress = openClass.recordCount + openClass.subscribeCount
            vo.hasSignIn = openClass.signInImg != null
            vo.signInImg = openClass.signInImg

            //申请退课信息
            val applyCancelList = allApplyCancelList.filter { it.classRecordId == openClass.id }
            vo.applyCancelClassCount = applyCancelList.size
            vo.applyCancelClassSuccessCount = applyCancelList.filter { it.status == ClassRecordApplyCancelStatus.PASS }.size
            vo.hasSettlement = openClass.hasSettle
            vo
        }
    }

    /**
     * app开课
     */
    @Transactional(rollbackFor = [Exception::class])
    fun appOpenClass(dto: AppOpenClassDTO): AppOpenClassVO {
        val course = courseService.getById(dto.courseId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "课程不存在")

        //是否指定舞室，需绑定舞室的时间信息
        var danceRoomTimeIdList: List<Int>? = null
        dto.danceRoomId?.run {
            val userId = currentAppUser().id

            if (dto.danceRoomStartTime == null || dto.danceRoomEndTime == null) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "请输入并确定绑定舞室的时间范围")
            }
            danceRoomTimeIdList = danceRoomTimeInfoService.unBindOpenClassListByTime(userId, this, dto.danceRoomStartTime!!, dto.danceRoomEndTime!!)
                .map { it.id }
            if (danceRoomTimeIdList!!.isEmpty()) {
                throw BizException(BizExceptionConst.CUSTOM_ERROR, "该时间段已全部被占用，请重新选择时间")
            }
        }

        //设置开课信息
        val openClass = openClassService.appOpenClass(dto, course.teacherId)

        //绑定舞室时段信息
        if (danceRoomTimeIdList?.isNotEmpty() == true) {
            val userId = currentAppUser().id
            danceRoomCommonAppService.bindOpenClass(userId, danceRoomTimeIdList!!, openClass.id)
        }

        //增加老师开课次数
        teacherApi.addOpenClassCount(course.teacherId)
        //增加课程开课次数
        courseService.addOpenClassCount(dto.courseId)
        return AppOpenClassVO().apply {
            openClassId = openClass.id
        }
    }

    /**
     * app老师侧开课记录详情
     */
    fun appTeacherSideClassDetailInfo(openClassId: Int): AppOpenClassTeacherSideDetailInfoVO {
        val openClass = openClassService.getById(openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        val course = courseService.getById(openClass.courseId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "课程不存在")
        val courseFileList = courseFileService.selectListByCourseId(openClass.courseId)
        val courseImgList = courseFileList.filter { it.type == "image" }
        val teacher = teacherApi.findById(openClass.teacherId)
        val site = openClass.siteId?.run {
            siteService.getById(openClass.siteId)
        }

        val simpleInfoVO = setOpenClassSimpleInfoVO(openClass, teacher, course, courseImgList, site, null, null)
        val detailInfoVO = AppOpenClassTeacherSideDetailInfoVO()
        BeanUtils.copyProperties(simpleInfoVO, detailInfoVO)
        detailInfoVO.classStatus = openClass.status
        detailInfoVO.recordNumber = openClass.recordCount
        detailInfoVO.memberPrice = openClass.memberPrice
        detailInfoVO.advanceBookingHours = openClass.advanceBookingHours
        detailInfoVO.bookingRestrictions = openClass.bookingRestrictions
        detailInfoVO.incomeMoney = calculateDivide(teacher.settleRatio, openClass.totalMoney)
        detailInfoVO.rowNumber = openClass.rowNumber
        detailInfoVO.columnNumber = openClass.columnNumber
        detailInfoVO.danceRoomName = openClass.danceRoomId?.run {
            danceRoomService.getById(openClass.danceRoomId)?.name
        } ?: openClass.customDanceRoomName ?: ""
        detailInfoVO.siteAddress = openClass.customSiteAddress?.run {
            "${openClass.customSiteProvinceAndCity}${openClass.customSiteCounty} $this"
        } ?: "${site!!.province}${site.city}${site.county ?: ""}${site.address}"
        detailInfoVO.imgs = courseImgList.map { it.url }
        detailInfoVO.videos = courseFileList.filter { it.type == "video" }.map { it.url }
        val fileList = openClassFileService.findAllByClassId(openClassId)
        detailInfoVO.videoList = fileList.filter { it.type == "video" }.map { it.url }
        detailInfoVO.imgList = fileList.filter { it.type == "image" }.map { it.url }
        detailInfoVO.openGroup = openClass.allowGroup
        detailInfoVO.customCityCode = openClass.customCityCode
        detailInfoVO.customSiteProvinceAndCity = openClass.customSiteProvinceAndCity
        detailInfoVO.customSiteCounty = openClass.customSiteCounty
        detailInfoVO.customSiteAddress = openClass.customSiteAddress
        detailInfoVO.customSiteName = openClass.customSiteName
        detailInfoVO.customSiteRemark = openClass.customSiteRemark
        detailInfoVO.customDanceRoomName = openClass.customDanceRoomName
        return detailInfoVO
    }

    /**
     * app老师侧修改开课信息
     */
    @Transactional(rollbackFor = [Exception::class])
    fun appModifyOpenClass(dto: AppOpenClassUpdateDTO) {
        //修改课程
        val success = courseService.updateCourse(dto.courseId, dto.courseName, dto.danceType, dto.difficulty, dto.ageRange, dto.desc)
        if (!success) throw BizException(BizExceptionConst.CUSTOM_ERROR, "修改开课信息失败，无权限或者课程不存在")
        courseFileService.deleteFileListByCourseId(dto.courseId)
        courseFileService.addImgListByCourseId(dto.courseId, dto.imgList)
        courseFileService.addVideoListByCourseId(dto.courseId, dto.videoList)

        //修改开课
        val openClass = openClassService.getById(dto.openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        openClassService.appModifyOpenClass(openClass, dto)
    }

    /**
     * app老师取消开课
     */
    fun appTeacherCancelOpenClass(dto: AppOpenClassTeacherCancelDTO) {
        val openClass = openClassService.getById(dto.openClassId) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        if (openClass.teacherId != currentAppUser().id) throw BizException(BizExceptionConst.CUSTOM_ERROR, "无权限取消开课")

        classCommonAppService.cancelClass(dto.openClassId, dto.cancelReason, 1, OpenClassStatus.PENDING)
    }

    /**
     * app上传开课文件
     */
    fun appUploadOpenClassFile(dto: AppOpenClassUploadFileDTO) {
        openClassFileService.addFile(dto.openClassId, dto.url, dto.suffix, dto.type)
    }

    /**
     * app老师开课签到
     */
    fun appSignOpenClass(dto: AppOpenClassTeacherSignInDTO) {
        openClassService.signIn(dto.openClassId, dto.img)
    }

    fun classInfo(id: Int): OpenClassBO {
        val openClass = openClassService.getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课信息不存在")
        return openClass.convertToBO<OpenClassBO>()!!
    }

    /**
     * app课程详情中的评价相关
     */
    fun appClassEvaluateList(dto: AppOpenClassEvaluateListDTO): ApiPage<AppOpenClassEvaluateListVO> {
        val pageList = evaluateRecordService.pageList(dto.page, dto.size, dto.type, dto.targetId)
        val userIdList = pageList.records.map { it.userId }
        val userList = userApi.findByIdList(userIdList)
        val voList = pageList.records.map { record ->
            val vo = AppOpenClassEvaluateListVO()
            vo.evaluateId = record.id
            vo.userId = record.userId
            val user = userList.first { it.id == record.userId }
            vo.nickname = user.nickname
            vo.pic = user.pic
            vo.content = record.content ?: ""
            vo.score = record.score
            vo.createTime = record.createTime.toLocalDate()
            vo
        }
        return pageList.build(voList)
    }

    fun applyRefundListByOrderIdList(orderIdList: List<String>): List<ClassRecordApplyCancel> {
        return classRecordApplyCancelService.selectUnDepositListByOrderIdList(orderIdList)
    }

    /**
     * admin同意约课申请退款
     */
    @Transactional(rollbackFor = [Exception::class])
    fun passApplyRefund(id: Int) {
        val pass = classRecordApplyCancelService.passApply(id)
        if (!pass) return

        val applyRecord = classRecordApplyCancelService.getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "申请退款信息不存在")
        val openClass = openClassService.getById(applyRecord.openClassId)
        if (openClass.hasSettle) throw BizException(BizExceptionConst.CUSTOM_ERROR, "开课已结算，无法退款")
        val classRecord = classRecordService.getById(applyRecord.classRecordId)

        val (coinRefund, weiXinRefund, _) = classCommonAppService.cancelClassRecord(classRecord, openClass, applyRecord.applyCancelReason, 1.0)
        platformInformService.add(
            "您于${applyRecord.createTime.format(LOCAL_DATE_TIME_FORMATTER)}提交的${openClass.price}退款申请已通过：D.Base钱包到账金币${coinRefund}，微信支付退款${weiXinRefund}，请注意查收",
            classRecord.studentId
        )
    }

    /**
     * admin拒绝约课申请退款
     */
    fun rejectApplyRefund(id: Int) {
        val reject = classRecordApplyCancelService.rejectApply(id)
        if (!reject) return

        val applyRecord = classRecordApplyCancelService.getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "申请退款信息不存在")
        val businessOrder = businessOrderService.getById(applyRecord.businessOrderId)

        //发送通知
        platformInformService.add(
            "您于${applyRecord.createTime.format(LOCAL_DATE_TIME_FORMATTER)}提交的${businessOrder.orderMoney}退款申请未通过，如有疑问请您联系客服",
            applyRecord.studentId
        )
    }

    @EventListener(CancelClassActionEvent::class)
    fun cancelClassEvent(event: CancelClassActionEvent) {
        event.apply {
            classCommonAppService.cancelClass(classId, reason, action, OpenClassStatus.PENDING)
        }
    }

    fun getTimetables(dto: OpenClassTimetableQueryDTO): OpenClassTimetableBO {
        // 处理anchorDate，如果为空则使用当前日期
        val anchorDate = if (dto.anchorDate.isNullOrBlank()) {
            LocalDate.now()
        } else {
            LocalDate.parse(dto.anchorDate)
        }

        // 计算anchorDate所在周的周一和周日
        val weekStart = anchorDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        val weekEnd = anchorDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))

        // 构建查询条件
        val queryWrapper = QueryWrapper.create()
        queryWrapper.whereWith {
            OpenClass::attendClassTime ge weekStart.atStartOfDay()
        }
        queryWrapper.whereWith {
            OpenClass::attendClassTime lt weekEnd.plusDays(1).atStartOfDay()
        }

        // 添加其他过滤条件
        dto.courseId?.run {
            queryWrapper.whereWith {
                OpenClass::courseId eq this
            }
        }

        dto.teacherId?.run {
            queryWrapper.whereWith {
                OpenClass::teacherId eq this
            }
        }

        if (!dto.customDanceRoomName.isNullOrBlank()) {
            queryWrapper.whereWith {
                OpenClass::customDanceRoomName like "%${dto.customDanceRoomName}%"
            }
        }

        if (!dto.status.isNullOrBlank()) {
            queryWrapper.whereWith {
                OpenClass::status eq dto.status
            }
        }

        // 查询数据
        val openClasses = openClassService.list(queryWrapper)

        // 获取课程和教师信息
        val courseIds = openClasses.map { it.courseId }.distinct()
        val teacherIds = openClasses.map { it.teacherId }.distinct()

        // 如果ids为空，则不去查询课程和教师信息，直接设置为空的Map
        val courseMap = if (courseIds.isEmpty()) emptyMap() else courseService.listByIds(courseIds).associateBy { it.id }
        val teacherMap = if (teacherIds.isEmpty()) emptyMap() else teacherApi.findByIds(teacherIds).associateBy { it.id }

        // 构建返回结果
        val result = OpenClassTimetableBO()
        val weeklyOpenClasses = mutableMapOf<String, List<OpenClassItemBO>>()
        val dailyClassCount = mutableMapOf<String, Int>()

        // 按日期分组
        val classMap = openClasses.groupBy {
            it.attendClassTime.toLocalDate().format(DateTimeFormatter.ISO_DATE)
        }

        // 遍历一周的每一天
        var currentDate = weekStart
        while (!currentDate.isAfter(weekEnd)) {
            val dateStr = currentDate.format(DateTimeFormatter.ISO_DATE)
            val classes = classMap[dateStr] ?: emptyList()

            // 检查冲突并标记
            val classesWithConflictFlag = classes.map { openClass ->
                val isConflict = checkConflict(openClass, classes)
                Pair(openClass, isConflict)
            }

            // 转换为BO
            val classBOs = classesWithConflictFlag.map { (openClass, isConflict) ->
                val bo = OpenClassItemBO()
                BeanUtils.copyProperties(openClass, bo)

                // 设置关联属性
                bo.courseName = courseMap[openClass.courseId]?.name
                bo.teacherName = teacherMap[openClass.teacherId]?.nickname
                bo.difficulty = courseMap[openClass.courseId]?.difficulty
                // 设置冲突标记
                bo.isConflict = isConflict
                bo
            }

            weeklyOpenClasses[dateStr] = classBOs
            dailyClassCount[dateStr] = classBOs.size

            currentDate = currentDate.plusDays(1)
        }

        result.weeklyOpenClasses = weeklyOpenClasses
        result.dailyClassCount = dailyClassCount

        return result
    }

    /**
     * 检查课程是否与同日其他课程冲突（教室冲突）
     */
    private fun checkConflict(openClass: OpenClass, classesSameDay: List<OpenClass>): Boolean {
        // 如果当前课程本身已取消或已结束，则不存在冲突
        if (openClass.status == OpenClassStatus.CANCEL || openClass.status == OpenClassStatus.FINISH) {
            return false
        }
        
        return classesSameDay.any { other ->
            // 跳过自己
            if (other.id == openClass.id) {
                return@any false
            }
            
            // 跳过已取消和已结束的课程
            if (other.status == OpenClassStatus.CANCEL || other.status == OpenClassStatus.FINISH) {
                return@any false
            }
            
            // 检查时间是否冲突
            val timeConflict = openClass.attendClassTime.isBefore(other.finishClassTime) && 
                             openClass.finishClassTime.isAfter(other.attendClassTime)
            
            if (timeConflict) {
                // 检查是否为同一教室（教室冲突）
                val roomConflict = !openClass.customDanceRoomName.isNullOrBlank() && 
                                 !other.customDanceRoomName.isNullOrBlank() &&
                                 openClass.customDanceRoomName == other.customDanceRoomName
                
                // 只检查教室冲突
                return@any roomConflict
            }
            
            false
        }
    }

    /**
     * 复制开课
     */
    @Transactional(rollbackFor = [Exception::class])
    fun copyOpenClass(dto: CopyOpenClassDTO) {
        // 参数验证
        if (dto.sourceStartDate == null || dto.sourceEndDate == null || 
            dto.targetStartDate == null || dto.targetEndDate == null) {
            throw BizException("日期参数不能为空")
        }
        
        if (dto.sourceStartDate!!.isAfter(dto.sourceEndDate)) {
            throw BizException("源开始日期不能晚于源结束日期")
        }
        
        if (dto.targetStartDate!!.isAfter(dto.targetEndDate)) {
            throw BizException("目标开始日期不能晚于目标结束日期")
        }

        // 校验源日期和目标日期的天数必须一样
        val sourceDays = dto.sourceStartDate!!.until(dto.sourceEndDate!!).days + 1
        val targetDays = dto.targetStartDate!!.until(dto.targetEndDate!!).days + 1
        if (sourceDays != targetDays) {
            throw BizException("源日期范围和目标日期范围的天数必须相同，当前源日期范围天数为${sourceDays}天，目标日期范围天数为${targetDays}天")
        }

        // 查询源日期范围内的开课数据
        val queryWrapper = QueryWrapper.create()
        queryWrapper.whereWith {
            OpenClass::attendClassTime ge dto.sourceStartDate!!.atStartOfDay()
        }

        queryWrapper.whereWith {
            OpenClass::attendClassTime lt dto.sourceEndDate!!.plusDays(1).atStartOfDay()
        }
        
        // 添加其他过滤条件
        dto.teacherId?.let {
            queryWrapper.whereWith {
                OpenClass::teacherId eq it
            }
        }
        
        dto.courseId?.let {
            queryWrapper.whereWith {
                OpenClass::courseId eq it
            }
        }
        
        // 查询数据
        val sourceOpenClasses = openClassService.list(queryWrapper)
        if (sourceOpenClasses.isEmpty()) {
            throw BizException("未找到符合条件的开课数据")
        }

        // 预检查所有冲突，有任何冲突直接抛出异常
        for (sourceOpenClass in sourceOpenClasses) {
            // 计算源日期在源日期范围内的偏移量（天数）
            val sourceDate = sourceOpenClass.attendClassTime.toLocalDate()
            val sourceOffset = dto.sourceStartDate!!.until(sourceDate).days
            
            // 计算目标日期
            val targetOffset = sourceOffset
            val targetDate = dto.targetStartDate!!.plusDays(targetOffset.toLong())
            
            // 检查目标日期是否在目标日期范围内
            if (targetDate.isBefore(dto.targetStartDate) || targetDate.isAfter(dto.targetEndDate)) {
                continue
            }
            
            // 检查教室冲突
            if (dto.skipClassroomConflict != true && sourceOpenClass.customDanceRoomName != null) {
                val roomConflictQuery = QueryWrapper.create()
                roomConflictQuery.whereWith {
                    OpenClass::customDanceRoomName eq sourceOpenClass.customDanceRoomName
                }
                roomConflictQuery.whereWith {
                    OpenClass::attendClassTime ge targetDate.atStartOfDay()
                }
                roomConflictQuery.whereWith {
                    OpenClass::attendClassTime lt targetDate.plusDays(1).atStartOfDay()
                }
                // 跳过已取消和已结束的课程
                roomConflictQuery.whereWith {
                    OpenClass::status ne OpenClassStatus.CANCEL
                }
                roomConflictQuery.whereWith {
                    OpenClass::status ne OpenClassStatus.FINISH
                }
                
                val roomConflict = openClassService.list(roomConflictQuery).any { 
                    val targetAttendTime = targetDate.atTime(sourceOpenClass.attendClassTime.toLocalTime())
                    val targetFinishTime = targetDate.atTime(sourceOpenClass.finishClassTime.toLocalTime())
                    targetAttendTime.isBefore(it.finishClassTime) && 
                    targetFinishTime.isAfter(it.attendClassTime)
                }
                
                if (roomConflict) {
                    throw BizException("目标日期 ${targetDate} 在该时间段内教室已被占用")
                }
            }
        }

        // 直接遍历源课程进行复制，有任何错误立即抛出异常
        for (sourceOpenClass in sourceOpenClasses) {
            // 计算目标日期
            val sourceDate = sourceOpenClass.attendClassTime.toLocalDate()
            val sourceOffset = dto.sourceStartDate!!.until(sourceDate).days
            val targetOffset = sourceOffset
            val targetDate = dto.targetStartDate!!.plusDays(targetOffset.toLong())
            
            // 检查目标日期是否在目标日期范围内
            if (targetDate.isBefore(dto.targetStartDate) || targetDate.isAfter(dto.targetEndDate)) {
                continue
            }
            
            // 直接创建新课程
            val appOpenClassDTO = AppOpenClassDTO().apply {
                courseId = sourceOpenClass.courseId
                danceRoomId = sourceOpenClass.danceRoomId
                siteId = sourceOpenClass.siteId
                date = targetDate
                startTime = LocalTime.of(sourceOpenClass.attendClassTime.hour, sourceOpenClass.attendClassTime.minute)
                endTime = LocalTime.of(sourceOpenClass.finishClassTime.hour, sourceOpenClass.finishClassTime.minute)
                customCityCode = sourceOpenClass.customCityCode
                customSiteProvinceAndCity = sourceOpenClass.customSiteProvinceAndCity
                customSiteCounty = sourceOpenClass.customSiteCounty
                customSiteAddress = sourceOpenClass.customSiteAddress
                customSiteName = sourceOpenClass.customSiteName
                customSiteRemark = sourceOpenClass.customSiteRemark
                customDanceRoomName = sourceOpenClass.customDanceRoomName
                maxNumber = sourceOpenClass.number
                columnNumber = sourceOpenClass.columnNumber
                rowNumber = sourceOpenClass.rowNumber
                openGroup = sourceOpenClass.allowGroup
                minRecordCount = sourceOpenClass.minRecordCount
                atLatestCancelTime = if (sourceOpenClass.atLatestCancelTime.toLocalDate() == sourceOpenClass.attendClassTime.toLocalDate()) {
                    // 如果最晚解散时间和上课时间是同一天，则保持相对时间关系
                    targetDate.atTime(sourceOpenClass.atLatestCancelTime.toLocalTime())
                } else {
                    // 否则计算相对天数差
                    val daysDiff = sourceOpenClass.attendClassTime.toLocalDate().until(sourceOpenClass.atLatestCancelTime.toLocalDate()).days
                    targetDate.plusDays(daysDiff.toLong()).atTime(sourceOpenClass.atLatestCancelTime.toLocalTime())
                }
                price = sourceOpenClass.price
                memberPrice = sourceOpenClass.memberPrice
                advanceBookingHours = sourceOpenClass.advanceBookingHours
                bookingRestrictions = sourceOpenClass.bookingRestrictions
            }
            
            // 立即创建课程，有任何错误会立即抛出异常并回滚
            openClassService.appOpenClass(appOpenClassDTO, sourceOpenClass.teacherId)
        }
    }
}