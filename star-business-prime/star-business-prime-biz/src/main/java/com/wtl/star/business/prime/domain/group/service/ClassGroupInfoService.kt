package com.wtl.star.business.prime.domain.group.service

import com.mybatisflex.core.util.UpdateEntity
import com.mybatisflex.kotlin.extensions.kproperty.eq
import com.mybatisflex.spring.service.impl.ServiceImpl
import com.wtl.star.business.prime.domain.course.event.OpenClassFinishEvent
import com.wtl.star.business.prime.domain.group.entity.ClassGroupInfo
import com.wtl.star.business.prime.domain.group.event.GroupSuccessSettleAcEvent
import com.wtl.star.business.prime.domain.group.mapper.ClassGroupInfoMapper
import com.wtl.star.common.config.Application
import com.wtl.star.common.exception.BizException
import com.wtl.star.common.exception.BizExceptionConst
import com.wtl.star.mybatis.extenions.condition
import jakarta.annotation.PostConstruct
import jakarta.annotation.PreDestroy
import org.redisson.api.RBlockingQueue
import org.redisson.api.RDelayedQueue
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationContext
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

@Service
class ClassGroupInfoService(
    private val redissonClient: RedissonClient
) : ServiceImpl<ClassGroupInfoMapper, ClassGroupInfo>() {

    @Value("\${application:APP}")
    private var application: Application = Application.APP

    private lateinit var settleBlockingQueue: RBlockingQueue<Int>
    private lateinit var settleDelayedQueue: RDelayedQueue<Int>
    private lateinit var settleThread: Thread

    @Autowired
    private lateinit var applicationContext: ApplicationContext

    @PostConstruct
    fun init() {
        if (application == Application.APP) {
            settleQueueListener()
        }
    }

    @PreDestroy
    fun destroy() {
        if (application == Application.APP) {
            settleThread.interrupt()
        }
    }

    fun openGroup(userId: Int, classId: Int): Int {
        val info = ClassGroupInfo()
        info.classId = classId
        info.ownerId = userId
        //开团人是团内第一个正式成员
        info.joinCount = 1
        info.successCount = 1
        mapper.insert(info)
        return info.id
    }

    fun joinGroup(id: Int) {
        val updateInfo = UpdateEntity.of(ClassGroupInfo::class.java, id)
        val info = getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "拼团信息不存在")
        updateInfo.joinCount = info.joinCount + 1
        mapper.update(updateInfo)
    }

    fun successJoinGroup(id: Int): Boolean {
        val updateInfo = UpdateEntity.of(ClassGroupInfo::class.java)
        val info = getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "拼团信息不存在")
        updateInfo.successCount = info.successCount + 1
        return mapper.updateByCondition(updateInfo, condition {
            ClassGroupInfo::id eqA id
            ClassGroupInfo::successCount ltA 5
        }) > 0
    }

    fun cancelSuccessJoinGroup(id: Int) {
        val updateInfo = UpdateEntity.of(ClassGroupInfo::class.java, id)
        val info = getById(id) ?: throw BizException(BizExceptionConst.CUSTOM_ERROR, "拼团信息不存在")
        updateInfo.successCount = info.successCount - 1
        mapper.update(updateInfo)
    }

    fun cancelGroupByIdList(idList: List<Int>) {
        if (idList.isEmpty()) return

        mapper.deleteBatchByIds(idList)
    }

    fun getGroupListByClassId(classId: Int): List<ClassGroupInfo> {
        return mapper.selectListByCondition(ClassGroupInfo::classId eq classId)
    }


    @EventListener(OpenClassFinishEvent::class)
    fun classFinishEvent(event: OpenClassFinishEvent) {
        event.apply {
            //拼团成功的发布72小时后返利消息
            val successGroupList = getSuccessGroupListByClassId(classId)
            successGroupList.forEach { group ->
                settleDelayedQueue.offer(group.id, 72, java.util.concurrent.TimeUnit.HOURS)
            }
        }
    }

    /**
     * 拼团成功结算队列监听
     */
    private fun settleQueueListener() {
        settleBlockingQueue = redissonClient.getBlockingQueue("group:settle:queue")
        settleDelayedQueue = redissonClient.getDelayedQueue(settleBlockingQueue)
        settleThread = Thread {
            while (!settleThread.isInterrupted) {
                kotlin.runCatching take@{
                    // 使用 poll 方法设置超时时间，避免无限期阻塞
                    val groupId = settleBlockingQueue.poll(30, TimeUnit.SECONDS) ?: return@take
                    //队列到期消费
                    applicationContext.publishEvent(GroupSuccessSettleAcEvent(groupId))
                }.onFailure {
                    if (it is InterruptedException) {
                        //抛出异常后中断状态被恢复，重新中断
                        settleThread.interrupt()
                    }
                }
            }
        }
        settleThread.start()
    }

    fun getSuccessGroupListByClassId(classId: Int): List<ClassGroupInfo> {
        return mapper.selectListByCondition(condition {
            ClassGroupInfo::classId eqA classId
            ClassGroupInfo::hasSettle eqA false
            ClassGroupInfo::successCount geA 3
        })
    }

    fun settleGroup(groupId: Int): Boolean {
        val updateInfo = UpdateEntity.of(ClassGroupInfo::class.java)
        updateInfo.hasSettle = true
        return mapper.updateByCondition(updateInfo, condition {
            ClassGroupInfo::id eqA groupId
            ClassGroupInfo::successCount geA 3
            ClassGroupInfo::hasSettle eqA false
        }) > 0
    }
}